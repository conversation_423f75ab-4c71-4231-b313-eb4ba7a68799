"""
综合数据比较分析脚本
用于比较grid_code与dandian、Bilinear之间的差异
包含多种统计指标、可视化图表和精度评估方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import gaussian_kde
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MeteorologicalAnalysis:
    """气象数据分析类"""
    
    def __init__(self, data_path):
        """初始化数据"""
        self.data = pd.read_csv(data_path)
        self.prepare_data()
        
    def prepare_data(self):
        """数据预处理"""
        # 清理列名中的空格
        self.data.columns = self.data.columns.str.strip()
        
        # 移除缺失值
        self.data = self.data.dropna()
        
        # 确保数据类型正确
        numeric_cols = ['grid_code', 'dandian', 'Bilinear']
        for col in numeric_cols:
            self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
        
        self.data = self.data.dropna()
        
        print(f"数据形状: {self.data.shape}")
        print(f"数据概览:")
        print(self.data.describe())
    
    def calculate_metrics(self, observed, predicted, name=""):
        """计算各种评估指标"""
        # 基本统计指标
        n = len(observed)
        
        # 相关系数
        pearson_r, pearson_p = stats.pearsonr(observed, predicted)
        spearman_r, spearman_p = stats.spearmanr(observed, predicted)
        
        # R² (决定系数)
        ss_res = np.sum((observed - predicted) ** 2)
        ss_tot = np.sum((observed - np.mean(observed)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # 调整R²
        adj_r_squared = 1 - (1 - r_squared) * (n - 1) / (n - 2) if n > 2 else r_squared
        
        # RMSE (均方根误差)
        rmse = np.sqrt(np.mean((observed - predicted) ** 2))
        
        # MAE (平均绝对误差)
        mae = np.mean(np.abs(observed - predicted))
        
        # Nash-Sutcliffe效率系数
        nse = 1 - (np.sum((observed - predicted) ** 2) / np.sum((observed - np.mean(observed)) ** 2))
        
        # 偏差 (Bias)
        bias = np.mean(predicted - observed)
        
        # 相对偏差 (%)
        relative_bias = (bias / np.mean(observed)) * 100 if np.mean(observed) != 0 else 0
        
        # 标准差比率
        std_ratio = np.std(predicted) / np.std(observed) if np.std(observed) != 0 else 0
        
        # 均值比率
        mean_ratio = np.mean(predicted) / np.mean(observed) if np.mean(observed) != 0 else 0
        
        # MAPE (平均绝对百分比误差)
        mape = np.mean(np.abs((observed - predicted) / observed)) * 100 if np.all(observed != 0) else np.inf
        
        metrics = {
            'comparison': name,
            'n_samples': n,
            'pearson_r': pearson_r,
            'pearson_p': pearson_p,
            'spearman_r': spearman_r,
            'spearman_p': spearman_p,
            'r_squared': r_squared,
            'adj_r_squared': adj_r_squared,
            'rmse': rmse,
            'mae': mae,
            'nse': nse,
            'bias': bias,
            'relative_bias': relative_bias,
            'std_ratio': std_ratio,
            'mean_ratio': mean_ratio,
            'mape': mape
        }
        
        return metrics
    
    def create_correlation_matrix(self):
        """创建相关性矩阵热力图"""
        plt.figure(figsize=(10, 8))
        
        # 计算相关矩阵
        corr_matrix = self.data[['grid_code', 'dandian', 'Bilinear']].corr()
        
        # 创建热力图
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        
        plt.title('相关性矩阵热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return corr_matrix
    
    def create_scatter_plots(self):
        """创建散点图对比"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # grid_code vs dandian
        axes[0].scatter(self.data['grid_code'], self.data['dandian'], 
                       alpha=0.6, s=20, color='blue', edgecolors='black', linewidth=0.5)
        axes[0].plot([self.data['grid_code'].min(), self.data['grid_code'].max()], 
                    [self.data['grid_code'].min(), self.data['grid_code'].max()], 
                    'r--', lw=2, label='1:1线')
        
        # 添加拟合线和R²
        z = np.polyfit(self.data['grid_code'], self.data['dandian'], 1)
        p = np.poly1d(z)
        axes[0].plot(self.data['grid_code'], p(self.data['grid_code']), 
                    'g-', lw=2, label=f'拟合线: y={z[0]:.4f}x+{z[1]:.6f}')
        
        # 计算R²
        y_pred = p(self.data['grid_code'])
        ss_res = np.sum((self.data['dandian'] - y_pred) ** 2)
        ss_tot = np.sum((self.data['dandian'] - np.mean(self.data['dandian'])) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        # 计算相关系数
        correlation = np.corrcoef(self.data['grid_code'], self.data['dandian'])[0, 1]
        
        axes[0].set_xlabel('Grid_code')
        axes[0].set_ylabel('Dandian')
        axes[0].set_title('Grid_code vs Dandian')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 添加统计信息文本框
        textstr = f'R² = {r_squared:.6f}\nr = {correlation:.6f}\nn = {len(self.data)}'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        axes[0].text(0.05, 0.95, textstr, transform=axes[0].transAxes, fontsize=10,
                    verticalalignment='top', bbox=props)
        
        # grid_code vs Bilinear
        axes[1].scatter(self.data['grid_code'], self.data['Bilinear'], 
                       alpha=0.6, s=20, color='red', edgecolors='black', linewidth=0.5)
        axes[1].plot([self.data['grid_code'].min(), self.data['grid_code'].max()], 
                    [self.data['grid_code'].min(), self.data['grid_code'].max()], 
                    'r--', lw=2, label='1:1线')
        
        # 添加拟合线和R²
        z = np.polyfit(self.data['grid_code'], self.data['Bilinear'], 1)
        p = np.poly1d(z)
        axes[1].plot(self.data['grid_code'], p(self.data['grid_code']), 
                    'g-', lw=2, label=f'拟合线: y={z[0]:.4f}x+{z[1]:.6f}')
        
        # 计算R²
        y_pred = p(self.data['grid_code'])
        ss_res = np.sum((self.data['Bilinear'] - y_pred) ** 2)
        ss_tot = np.sum((self.data['Bilinear'] - np.mean(self.data['Bilinear'])) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        # 计算相关系数
        correlation = np.corrcoef(self.data['grid_code'], self.data['Bilinear'])[0, 1]
        
        axes[1].set_xlabel('Grid_code')
        axes[1].set_ylabel('Bilinear')
        axes[1].set_title('Grid_code vs Bilinear')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 添加统计信息文本框
        textstr = f'R² = {r_squared:.6f}\nr = {correlation:.6f}\nn = {len(self.data)}'
        props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
        axes[1].text(0.05, 0.95, textstr, transform=axes[1].transAxes, fontsize=10,
                    verticalalignment='top', bbox=props)
        
        plt.tight_layout()
        plt.savefig('scatter_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_distribution_plots(self):
        """创建分布直方图和密度图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 直方图
        axes[0, 0].hist(self.data['grid_code'], bins=50, alpha=0.7, label='Grid_code', color='blue', density=True)
        axes[0, 0].hist(self.data['dandian'], bins=50, alpha=0.7, label='Dandian', color='red', density=True)
        axes[0, 0].hist(self.data['Bilinear'], bins=50, alpha=0.7, label='Bilinear', color='green', density=True)
        axes[0, 0].set_xlabel('数值')
        axes[0, 0].set_ylabel('密度')
        axes[0, 0].set_title('数值分布直方图')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 核密度估计
        for col, color in zip(['grid_code', 'dandian', 'Bilinear'], ['blue', 'red', 'green']):
            data_col = self.data[col]
            kde = gaussian_kde(data_col)
            x_range = np.linspace(data_col.min(), data_col.max(), 200)
            axes[0, 1].plot(x_range, kde(x_range), label=col, color=color, linewidth=2)
        
        axes[0, 1].set_xlabel('数值')
        axes[0, 1].set_ylabel('密度')
        axes[0, 1].set_title('核密度估计')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 箱线图
        data_for_box = [self.data['grid_code'], self.data['dandian'], self.data['Bilinear']]
        axes[1, 0].boxplot(data_for_box, labels=['Grid_code', 'Dandian', 'Bilinear'])
        axes[1, 0].set_ylabel('数值')
        axes[1, 0].set_title('箱线图')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Q-Q图 (Dandian vs Grid_code)
        stats.probplot(self.data['dandian'] - self.data['grid_code'], dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('Q-Q图 (Dandian-Grid_code残差)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('distribution_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_taylor_diagram(self):
        """创建标准化泰勒图"""
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 参考数据 (grid_code)
        ref_data = self.data['grid_code']
        ref_std = np.std(ref_data)
        
        # 测试数据集
        datasets = {
            'Dandian': self.data['dandian'],
            'Bilinear': self.data['Bilinear']
        }
        
        colors = ['red', 'blue']
        markers = ['o', 's']
        
        # 绘制参考点 (标准化标准差 = 1.0)
        ax.plot(0, 1.0, 'k*', markersize=15, label='Grid_code (参考)', 
               markeredgecolor='black', markeredgewidth=1)
        
        # 计算并绘制测试数据点
        for i, (name, data) in enumerate(datasets.items()):
            # 计算相关系数
            corr = np.corrcoef(ref_data, data)[0, 1]
            
            # 计算标准化标准差
            std_normalized = np.std(data) / ref_std
            
            # 将相关系数转换为角度
            theta = np.arccos(np.clip(corr, -1, 1))
            
            # 计算标准化RMSE
            rmse = np.sqrt(np.mean((ref_data - data)**2))
            rmse_normalized = rmse / ref_std
            
            # 绘制数据点
            ax.plot(theta, std_normalized, markers[i], color=colors[i], markersize=12, 
                   label=f'{name}\nr={corr:.3f}, RMSE={rmse_normalized:.3f}', 
                   markeredgecolor='black', markeredgewidth=1)
        
        # 设置坐标轴范围
        max_radius = 2.0  # 标准化后的最大半径
        ax.set_xlim(0, np.pi/2)
        ax.set_ylim(0, max_radius)
        
        # 设置角度网格线和标签 (相关系数)
        correlation_angles = np.array([0, 15, 30, 45, 60, 75, 90]) * np.pi / 180
        correlation_values = np.cos(correlation_angles)
        ax.set_thetagrids(np.degrees(correlation_angles), 
                         [f'{val:.2f}' for val in correlation_values])
        
        # 设置径向网格线 (标准化标准差)
        std_ticks = np.array([0.0, 0.5, 1.0, 1.5, 2.0])
        ax.set_yticks(std_ticks)
        ax.set_yticklabels([f'{tick:.1f}' for tick in std_ticks])
        
        # 添加标准化RMS等值线
        rmse_levels = np.array([0.2, 0.5, 0.8, 1.0, 1.2, 1.5])
        theta_range = np.linspace(0, np.pi/2, 100)
        
        for rmse_norm in rmse_levels:
            if rmse_norm <= max_radius:
                # 标准化RMS等值线方程: RMSE² = σ_f² + 1 - 2*σ_f*cos(θ)
                # 其中 σ_f 是标准化标准差，参考标准差已标准化为1
                rms_curve = []
                for theta in theta_range:
                    # 求解标准化标准差 σ_f
                    # rmse² = σ_f² + 1 - 2*σ_f*cos(θ)
                    # σ_f² - 2*cos(θ)*σ_f + (1 - rmse²) = 0
                    a = 1
                    b = -2 * np.cos(theta)
                    c = 1 - rmse_norm**2
                    discriminant = b**2 - 4*a*c
                    if discriminant >= 0:
                        sigma_f = (-b + np.sqrt(discriminant)) / (2*a)
                        if sigma_f >= 0 and sigma_f <= max_radius:
                            rms_curve.append((theta, sigma_f))
                
                if rms_curve:
                    rms_theta, rms_sigma = zip(*rms_curve)
                    ax.plot(rms_theta, rms_sigma, '--', color='gray', alpha=0.6, linewidth=1.5)
                    # 添加RMS标签
                    if len(rms_theta) > 10:
                        mid_idx = len(rms_theta) // 3
                        ax.text(rms_theta[mid_idx], rms_sigma[mid_idx], f'{rmse_norm:.1f}', 
                               fontsize=9, ha='center', va='bottom', color='gray',
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        
        # 添加相关系数径向线
        for corr_val in [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]:
            theta_corr = np.arccos(corr_val)
            if theta_corr <= np.pi/2:
                ax.plot([theta_corr, theta_corr], [0, max_radius], ':', 
                       color='lightblue', alpha=0.6, linewidth=1)
        
        # 添加标准差同心圆
        for std_val in std_ticks[1:]:
            theta_arc = np.linspace(0, np.pi/2, 100)
            ax.plot(theta_arc, [std_val] * len(theta_arc), ':', 
                   color='lightgreen', alpha=0.6, linewidth=1)
        
        # 设置标题和标签
        ax.set_title('标准化泰勒图\n(以Grid_code为参考)', y=1.15, fontsize=16, fontweight='bold')
        
        # 添加标签说明
        ax.text(0, max_radius*1.1, '相关系数', ha='center', fontsize=12, fontweight='bold')
        ax.text(-np.pi/6, max_radius*0.6, '标准化\n标准差', ha='center', fontsize=11, 
               fontweight='bold', rotation=30)
        
        # 添加RMS等值线说明
        ax.text(np.pi/3, max_radius*0.3, '标准化RMSE', ha='center', fontsize=10, 
               style='italic', color='gray', rotation=-45,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8))
        
        # 设置图例
        ax.legend(loc='upper left', bbox_to_anchor=(1.2, 1.0), fontsize=10)
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        
        # 添加说明文本
        explanation = ("标准化说明：\n• 标准差相对于参考数据标准差\n• RMSE相对于参考数据标准差\n• 参考点(Grid_code)位于(0,1)")
        ax.text(1.35, 0.3, explanation, transform=ax.transAxes, fontsize=9,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8),
               verticalalignment='top')
        
        plt.tight_layout()
        plt.savefig('taylor_diagram.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_residual_analysis(self):
        """创建残差分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 计算残差
        residual_dandian = self.data['dandian'] - self.data['grid_code']
        residual_bilinear = self.data['Bilinear'] - self.data['grid_code']
        
        # 残差vs预测值
        axes[0, 0].scatter(self.data['grid_code'], residual_dandian, alpha=0.6, color='blue', label='Dandian')
        axes[0, 0].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[0, 0].set_xlabel('Grid_code')
        axes[0, 0].set_ylabel('残差 (Dandian - Grid_code)')
        axes[0, 0].set_title('Dandian残差分析')
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].scatter(self.data['grid_code'], residual_bilinear, alpha=0.6, color='red', label='Bilinear')
        axes[0, 1].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[0, 1].set_xlabel('Grid_code')
        axes[0, 1].set_ylabel('残差 (Bilinear - Grid_code)')
        axes[0, 1].set_title('Bilinear残差分析')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 残差直方图
        axes[1, 0].hist(residual_dandian, bins=50, alpha=0.7, color='blue', density=True, label='Dandian残差')
        axes[1, 0].axvline(x=np.mean(residual_dandian), color='red', linestyle='--', 
                          label=f'均值: {np.mean(residual_dandian):.6f}')
        axes[1, 0].set_xlabel('残差值')
        axes[1, 0].set_ylabel('密度')
        axes[1, 0].set_title('Dandian残差分布')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].hist(residual_bilinear, bins=50, alpha=0.7, color='red', density=True, label='Bilinear残差')
        axes[1, 1].axvline(x=np.mean(residual_bilinear), color='red', linestyle='--', 
                          label=f'均值: {np.mean(residual_bilinear):.6f}')
        axes[1, 1].set_xlabel('残差值')
        axes[1, 1].set_ylabel('密度')
        axes[1, 1].set_title('Bilinear残差分布')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('residual_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("=" * 80)
        print("开始综合数据分析...")
        print("=" * 80)
        
        # 1. 计算评估指标
        print("\n1. 计算评估指标...")
        metrics_dandian = self.calculate_metrics(self.data['grid_code'], self.data['dandian'], 
                                                 "Grid_code vs Dandian")
        metrics_bilinear = self.calculate_metrics(self.data['grid_code'], self.data['Bilinear'], 
                                                 "Grid_code vs Bilinear")
        
        # 创建指标对比表
        metrics_df = pd.DataFrame([metrics_dandian, metrics_bilinear])
        
        print("\n评估指标对比表:")
        print("=" * 120)
        for index, row in metrics_df.iterrows():
            print(f"\n{row['comparison']}:")
            print(f"  样本数量: {row['n_samples']}")
            print(f"  Pearson相关系数: {row['pearson_r']:.6f} (p值: {row['pearson_p']:.6f})")
            print(f"  Spearman相关系数: {row['spearman_r']:.6f} (p值: {row['spearman_p']:.6f})")
            print(f"  R² (决定系数): {row['r_squared']:.6f}")
            print(f"  调整R²: {row['adj_r_squared']:.6f}")
            print(f"  RMSE (均方根误差): {row['rmse']:.8f}")
            print(f"  MAE (平均绝对误差): {row['mae']:.8f}")
            print(f"  Nash-Sutcliffe效率: {row['nse']:.6f}")
            print(f"  偏差 (Bias): {row['bias']:.8f}")
            print(f"  相对偏差 (%): {row['relative_bias']:.4f}%")
            print(f"  标准差比率: {row['std_ratio']:.6f}")
            print(f"  均值比率: {row['mean_ratio']:.6f}")
            print(f"  MAPE (%): {row['mape']:.4f}%")
        
        # 保存指标到CSV
        metrics_df.to_csv('evaluation_metrics.csv', index=False, encoding='utf-8-sig')
        
        # 2. 创建可视化图表
        print("\n2. 创建相关性矩阵...")
        corr_matrix = self.create_correlation_matrix()
        
        print("\n3. 创建散点图...")
        self.create_scatter_plots()
        
        print("\n4. 创建分布图...")
        self.create_distribution_plots()
        
        print("\n5. 创建泰勒图...")
        self.create_taylor_diagram()
        
        print("\n6. 创建残差分析图...")
        self.create_residual_analysis()
        
        # 3. 数据质量评估
        print("\n7. 数据质量评估...")
        self.data_quality_assessment()
        
        print("\n=" * 80)
        print("综合分析完成！所有图表和结果已保存。")
        print("=" * 80)
        
        return metrics_df, corr_matrix
    
    def data_quality_assessment(self):
        """数据质量评估"""
        print("\n数据质量评估:")
        print("-" * 40)
        
        # 基本统计信息
        print("基本统计信息:")
        print(self.data[['grid_code', 'dandian', 'Bilinear']].describe())
        
        # 异常值检测 (使用IQR方法)
        print("\n异常值检测 (IQR方法):")
        for col in ['grid_code', 'dandian', 'Bilinear']:
            Q1 = self.data[col].quantile(0.25)
            Q3 = self.data[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = self.data[(self.data[col] < lower_bound) | (self.data[col] > upper_bound)]
            print(f"  {col}: {len(outliers)} 个异常值 ({len(outliers)/len(self.data)*100:.2f}%)")
        
        # 数据范围
        print("\n数据范围:")
        for col in ['grid_code', 'dandian', 'Bilinear']:
            print(f"  {col}: [{self.data[col].min():.8f}, {self.data[col].max():.8f}]")
        
        # 零值统计
        print("\n零值统计:")
        for col in ['grid_code', 'dandian', 'Bilinear']:
            zero_count = (self.data[col] == 0).sum()
            print(f"  {col}: {zero_count} 个零值 ({zero_count/len(self.data)*100:.2f}%)")


def main():
    """主函数"""
    # 创建分析对象
    analysis = MeteorologicalAnalysis('点.csv')
    
    # 运行综合分析
    metrics_df, corr_matrix = analysis.run_comprehensive_analysis()
    
    # 输出结论和建议
    print("\n" + "=" * 80)
    print("插值方法比较分析结论:")
    print("=" * 80)
    
    dandian_r2 = metrics_df[metrics_df['comparison'] == 'Grid_code vs Dandian']['r_squared'].iloc[0]
    bilinear_r2 = metrics_df[metrics_df['comparison'] == 'Grid_code vs Bilinear']['r_squared'].iloc[0]
    
    dandian_rmse = metrics_df[metrics_df['comparison'] == 'Grid_code vs Dandian']['rmse'].iloc[0]
    bilinear_rmse = metrics_df[metrics_df['comparison'] == 'Grid_code vs Bilinear']['rmse'].iloc[0]
    
    dandian_corr = metrics_df[metrics_df['comparison'] == 'Grid_code vs Dandian']['pearson_r'].iloc[0]
    bilinear_corr = metrics_df[metrics_df['comparison'] == 'Grid_code vs Bilinear']['pearson_r'].iloc[0]
    
    dandian_nse = metrics_df[metrics_df['comparison'] == 'Grid_code vs Dandian']['nse'].iloc[0]
    bilinear_nse = metrics_df[metrics_df['comparison'] == 'Grid_code vs Bilinear']['nse'].iloc[0]
    
    print(f"\n【以Grid_code为真值，比较两种插值方法的表现】")
    print(f"\n1. 相关性比较:")
    print(f"   • Dandian相关系数: {dandian_corr:.6f}")
    print(f"   • Bilinear相关系数: {bilinear_corr:.6f}")
    if bilinear_corr > dandian_corr:
        print(f"   → Bilinear插值方法与参考值相关性更强")
    else:
        print(f"   → Dandian插值方法与参考值相关性更强")
    
    print(f"\n2. R²比较 (解释变异能力):")
    print(f"   • Dandian: {dandian_r2:.6f} ({dandian_r2*100:.2f}%)")
    print(f"   • Bilinear: {bilinear_r2:.6f} ({bilinear_r2*100:.2f}%)")
    if bilinear_r2 > dandian_r2:
        print(f"   → Bilinear方法能解释更多的数据变异")
    else:
        print(f"   → Dandian方法能解释更多的数据变异")
    
    print(f"\n3. RMSE比较 (预测精度):")
    print(f"   • Dandian: {dandian_rmse:.8f}")
    print(f"   • Bilinear: {bilinear_rmse:.8f}")
    if bilinear_rmse < dandian_rmse:
        print(f"   → Bilinear方法预测精度更高 (RMSE更小)")
        print(f"   → 精度提升: {((dandian_rmse-bilinear_rmse)/dandian_rmse)*100:.2f}%")
    else:
        print(f"   → Dandian方法预测精度更高 (RMSE更小)")
        print(f"   → 精度提升: {((bilinear_rmse-dandian_rmse)/bilinear_rmse)*100:.2f}%")
    
    print(f"\n4. Nash-Sutcliffe效率比较 (模型性能):")
    print(f"   • Dandian NSE: {dandian_nse:.6f}")
    print(f"   • Bilinear NSE: {bilinear_nse:.6f}")
    if bilinear_nse > dandian_nse:
        print(f"   → Bilinear方法模型效率更高")
    else:
        print(f"   → Dandian方法模型效率更高")
    print(f"   注：NSE>0.75为很好，0.65-0.75为好，0.5-0.65为满意，<0.5为不满意")
    
    # 综合评价
    print(f"\n5. 综合评价:")
    dandian_score = 0
    bilinear_score = 0
    
    if dandian_corr > bilinear_corr:
        dandian_score += 1
        print(f"   • 相关性: Dandian胜")
    else:
        bilinear_score += 1
        print(f"   • 相关性: Bilinear胜")
    
    if dandian_r2 > bilinear_r2:
        dandian_score += 1
        print(f"   • R²: Dandian胜")
    else:
        bilinear_score += 1
        print(f"   • R²: Bilinear胜")
    
    if dandian_rmse < bilinear_rmse:
        dandian_score += 1
        print(f"   • RMSE: Dandian胜")
    else:
        bilinear_score += 1
        print(f"   • RMSE: Bilinear胜")
    
    if dandian_nse > bilinear_nse:
        dandian_score += 1
        print(f"   • NSE: Dandian胜")
    else:
        bilinear_score += 1
        print(f"   • NSE: Bilinear胜")
    
    print(f"\n   综合得分: Dandian({dandian_score}) vs Bilinear({bilinear_score})")
    if dandian_score > bilinear_score:
        print(f"   🏆 推荐使用: Dandian插值方法")
    elif bilinear_score > dandian_score:
        print(f"   🏆 推荐使用: Bilinear插值方法")
    else:
        print(f"   🤝 两种方法表现相当，可根据具体需求选择")
    
    print(f"\n6. 气象数据插值评估建议:")
    print(f"   • 当前两种方法的相关性都在0.69以上，属于较强相关")
    print(f"   • R²值约为48%，说明还有约52%的变异未被解释")
    print(f"   • 建议考虑以下改进方向：")
    print(f"     - 检查数据质量和异常值处理")
    print(f"     - 考虑非线性插值方法")
    print(f"     - 增加更多空间变量或辅助信息")
    print(f"     - 评估不同区域的插值效果差异")


if __name__ == "__main__":
    main()
