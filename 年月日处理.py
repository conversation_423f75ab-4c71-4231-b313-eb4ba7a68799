import xarray as xr
import os
import glob
import logging
from tqdm import tqdm
import multiprocessing as mp
from functools import partial

# --- 用户需要修改的参数 ---
# 1. 将此路径更改为您的多个NC文件所在的文件夹
input_directory = 'E:/HRCLDAS' 

# 2. 您希望存放结果的文件夹路径
output_directory = 'E:/HRCLDAS'

# 3. 多核设置 (0 表示自动，使用所有核心)
#    您可以指定具体的数字，例如 n_workers=4 代表使用4个核心
num_cores_to_use = 0 
# --- 用户需要修改的参数 ---

# 配置日志
def setup_logging():
    # 创建日志格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 文件处理器
    file_handler = logging.FileHandler('processing.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # 控制台处理器 - 只输出到文件，避免与进度条冲突
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.WARNING)  # 只显示警告和错误
    
    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )
    return logging.getLogger(__name__)

# --- 脚本主体 ---

def preprocess_qair(ds):
    if 'level' in ds.sizes and ds.sizes['level'] == 1:
        return ds.squeeze('level', drop=True)
    return ds

def process_single_variable(var_name, input_dir, output_dir):
    """单个变量的处理函数，用于多进程"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始处理变量: {var_name}")

    daily_output_path = os.path.join(output_dir, 'daily_avg')
    monthly_output_path = os.path.join(output_dir, 'monthly_avg')
    annual_output_path = os.path.join(output_dir, 'annual_avg')
    os.makedirs(daily_output_path, exist_ok=True)
    os.makedirs(monthly_output_path, exist_ok=True)
    os.makedirs(annual_output_path, exist_ok=True)

    file_pattern = os.path.join(input_dir, f'*_HOR-{var_name}-2024-*.nc')
    monthly_files = sorted(glob.glob(file_pattern))

    if not monthly_files:
        logger.warning(f"在路径 '{input_dir}' 中未找到变量 '{var_name}' 的文件。")
        return f"{var_name}: 未找到文件"

    logger.info(f"找到 {len(monthly_files)} 个 {var_name} 的月度文件，将逐月处理。")

    try:
        # 逐月处理每个文件
        daily_files_created = []
        monthly_files_created = []
        
        # 处理每个月的文件
        for monthly_file in tqdm(monthly_files, desc=f"[{var_name}] 处理月度文件", leave=False):
            logger.info(f"正在处理文件: {os.path.basename(monthly_file)}")
            
            # 提取月份信息
            filename = os.path.basename(monthly_file)
            prefix = filename.split('-2024-')[0]
            month_part = filename.split('-2024-')[1].replace('.nc', '')  # 例如 "01", "02" 等
            
            with xr.open_dataset(monthly_file, chunks={'time': 720}) as ds_month_raw:
                # 手动应用预处理
                ds_month = preprocess_qair(ds_month_raw)
                
                # --- 计算该月的日平均 ---
                logger.info(f"正在计算 {month_part} 月的日平均...")
                daily_avg = ds_month.resample(time='1D').mean(keep_attrs=True)
                daily_output_filename = os.path.join(daily_output_path, f'{prefix}-2024-{month_part}_daily_avg.nc')
                daily_avg.to_netcdf(daily_output_filename)
                daily_files_created.append(os.path.basename(daily_output_filename))
                logger.info(f"日平均文件已保存: {os.path.basename(daily_output_filename)}")
                
                # --- 计算该月的月平均 ---
                logger.info(f"正在计算 {month_part} 月的月平均...")
                monthly_avg = ds_month.mean(dim='time', keep_attrs=True)
                monthly_output_filename = os.path.join(monthly_output_path, f'{prefix}-2024-{month_part}_monthly_avg.nc')
                monthly_avg.to_netcdf(monthly_output_filename)
                monthly_files_created.append(os.path.basename(monthly_output_filename))
                logger.info(f"月平均文件已保存: {os.path.basename(monthly_output_filename)}")
        
        # --- 计算年平均（需要合并所有月份数据）---
        logger.info("正在计算年平均...")
        with xr.open_mfdataset(monthly_files, 
                               combine='by_coords', 
                               preprocess=preprocess_qair, 
                               chunks={'time': 720}) as ds_year:
            annual_avg = ds_year.mean(dim='time', keep_attrs=True)
            prefix = os.path.basename(monthly_files[0]).split('-2024-')[0]
            annual_output_filename = os.path.join(annual_output_path, f'{prefix}-2024_annual_avg.nc')
            annual_avg.to_netcdf(annual_output_filename)
            logger.info(f"年平均文件已保存: {os.path.basename(annual_output_filename)}")
        
        logger.info(f"变量 {var_name} 处理完成:")
        logger.info(f"  创建了 {len(daily_files_created)} 个日平均文件")
        logger.info(f"  创建了 {len(monthly_files_created)} 个月平均文件")
        logger.info(f"  创建了 1 个年平均文件")
        
        return f"{var_name}: 处理完成"

    except Exception as e:
        error_msg = f"处理 {var_name} 时发生严重错误: {e}"
        logger.error(error_msg)
        return f"{var_name}: 处理失败 - {e}"

def process_variable_fully(var_name, input_dir, output_dir, logger):
    """主处理函数，用于单进程处理"""
    logger.info(f"开始处理变量: {var_name}")

    daily_output_path = os.path.join(output_dir, 'daily_avg')
    monthly_output_path = os.path.join(output_dir, 'monthly_avg')
    annual_output_path = os.path.join(output_dir, 'annual_avg')
    os.makedirs(daily_output_path, exist_ok=True)
    os.makedirs(monthly_output_path, exist_ok=True)
    os.makedirs(annual_output_path, exist_ok=True)

    file_pattern = os.path.join(input_dir, f'*_HOR-{var_name}-2024-*.nc')
    monthly_files = sorted(glob.glob(file_pattern))

    if not monthly_files:
        logger.warning(f"在路径 '{input_dir}' 中未找到变量 '{var_name}' 的文件。")
        return

    logger.info(f"找到 {len(monthly_files)} 个 {var_name} 的月度文件，将逐月处理。")

    try:
        # 逐月处理每个文件
        daily_files_created = []
        monthly_files_created = []
        
        # 处理每个月的文件
        for monthly_file in tqdm(monthly_files, desc=f"[{var_name}] 处理月度文件", leave=False, position=1):
            logger.info(f"正在处理文件: {os.path.basename(monthly_file)}")
            
            # 提取月份信息
            filename = os.path.basename(monthly_file)
            prefix = filename.split('-2024-')[0]
            month_part = filename.split('-2024-')[1].replace('.nc', '')  # 例如 "01", "02" 等
            
            with xr.open_dataset(monthly_file, chunks={'time': 720}) as ds_month_raw:
                # 手动应用预处理
                ds_month = preprocess_qair(ds_month_raw)
                
                # --- 计算该月的日平均 ---
                logger.info(f"正在计算 {month_part} 月的日平均...")
                daily_avg = ds_month.resample(time='1D').mean(keep_attrs=True)
                daily_output_filename = os.path.join(daily_output_path, f'{prefix}-2024-{month_part}_daily_avg.nc')
                daily_avg.to_netcdf(daily_output_filename)
                daily_files_created.append(os.path.basename(daily_output_filename))
                logger.info(f"日平均文件已保存: {os.path.basename(daily_output_filename)}")
                
                # --- 计算该月的月平均 ---
                logger.info(f"正在计算 {month_part} 月的月平均...")
                monthly_avg = ds_month.mean(dim='time', keep_attrs=True)
                monthly_output_filename = os.path.join(monthly_output_path, f'{prefix}-2024-{month_part}_monthly_avg.nc')
                monthly_avg.to_netcdf(monthly_output_filename)
                monthly_files_created.append(os.path.basename(monthly_output_filename))
                logger.info(f"月平均文件已保存: {os.path.basename(monthly_output_filename)}")
        
        # --- 计算年平均（需要合并所有月份数据）---
        logger.info("正在计算年平均...")
        with xr.open_mfdataset(monthly_files, 
                               combine='by_coords', 
                               preprocess=preprocess_qair, 
                               chunks={'time': 720}) as ds_year:
            annual_avg = ds_year.mean(dim='time', keep_attrs=True)
            prefix = os.path.basename(monthly_files[0]).split('-2024-')[0]
            annual_output_filename = os.path.join(annual_output_path, f'{prefix}-2024_annual_avg.nc')
            annual_avg.to_netcdf(annual_output_filename)
            logger.info(f"年平均文件已保存: {os.path.basename(annual_output_filename)}")
        
        logger.info(f"变量 {var_name} 处理完成:")
        logger.info(f"  创建了 {len(daily_files_created)} 个日平均文件")
        logger.info(f"  创建了 {len(monthly_files_created)} 个月平均文件")
        logger.info(f"  创建了 1 个年平均文件")

    except Exception as e:
        logger.error(f"处理 {var_name} 时发生严重错误: {e}")

# --- 主程序入口 ---
if __name__ == "__main__":
    logger = setup_logging()
    
    # 检查输入路径是否有效
    if input_directory == '.':
        # 当前文件夹是有效的
        pass
    elif not os.path.isdir(input_directory):
        logger.error("请将 'input_directory' 变量修改为您正确的NC文件文件夹路径。")
        exit(1)
    else:
        # 如果输出路径不存在或为默认值，则自动创建
        if output_directory == '.':
            output_directory = os.path.join(input_directory, 'analysis_results_parallel')
            logger.info(f"输出路径未指定，将使用默认路径: {output_directory}")
        
        if not os.path.exists(output_directory):
            os.makedirs(output_directory, exist_ok=True)

        # 设置进程数
        n_workers = num_cores_to_use if num_cores_to_use > 0 else mp.cpu_count()
        logger.info(f"使用 {n_workers} 个进程进行并行计算。")
        
        # 定义要处理的变量列表
        variables = ['QAIR', 'TAIR']
        
        # 使用多进程并行处理
        with mp.Pool(processes=n_workers) as pool:
            # 创建偏函数，固定输入和输出目录参数
            process_func = partial(process_single_variable, 
                                 input_dir=input_directory, 
                                 output_dir=output_directory)
            
            # 使用进度条显示总体进度
            with tqdm(total=len(variables), desc="总体进度", position=0) as pbar:
                results = []
                for result in pool.imap_unordered(process_func, variables):
                    results.append(result)
                    pbar.update(1)
                    pbar.set_postfix_str(f"当前: {result}")
        
        # 输出处理结果摘要
        logger.info("处理结果摘要:")
        for result in results:
            logger.info(f"  {result}")
        
        logger.info(f"所有处理已完成。结果已保存在 '{output_directory}' 的子文件夹中。")


