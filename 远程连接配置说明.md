# SQL Server 远程连接配置说明

## 问题诊断结果

✅ **Python 环境**: pyodbc 5.1.0 已安装
✅ **ODBC 驱动程序**: 检测到 SQL Server 驱动程序
❌ **本地服务**: 本地没有安装SQL Server实例

## 解决方案

您需要连接到**远程SQL Server服务器**。请按照以下步骤配置：

### 1. 运行配置工具

工具已经启动，请根据提示完成配置：

```
📋 可用的SQL Server驱动程序:
  1. SQL Server
  2. SQL Server Native Client 10.0
请选择驱动程序 (1-2):
```

**建议选择**: `1` (SQL Server 驱动程序通常兼容性更好)

### 2. 输入服务器信息

您需要提供以下信息：

- **服务器地址**: 远程SQL Server的IP地址或域名
- **端口号**: 通常是1433 (默认)
- **数据库名称**: SURF_DAAS
- **身份验证方式**: 
  - 选择1: Windows身份验证 (如果服务器在域环境中)
  - 选择2: SQL Server身份验证 (需要用户名和******)

### 3. 常见的连接字符串格式

根据您的具体情况，连接字符串可能是：

**Windows身份验证**:
```
Driver={SQL Server};Server=服务器IP;Database=SURF_DAAS;Trusted_Connection=yes;
```

**SQL Server身份验证**:
```
Driver={SQL Server};Server=服务器IP;Database=SURF_DAAS;UID=用户名;PWD=******;
```

### 4. 如果不知道服务器信息

如果您不确定服务器信息，请检查：

1. **现有配置文件**: 查看是否有其他应用程序的配置文件包含数据库连接信息
2. **网络管理员**: 联系IT管理员获取数据库服务器信息
3. **应用程序文档**: 查看相关应用程序的文档或配置说明

### 5. 网络连接测试

工具会自动测试网络连接。如果失败，可能的原因：

- 服务器地址错误
- 端口被防火墙阻止
- 服务器未启用远程连接
- 网络不通

### 6. 连接成功后

一旦配置成功，工具会：

1. 创建 `sql_connection_config.py` 配置文件
2. 测试数据库连接和目标表访问
3. 验证能否访问 SURF_GX_MUL_HOR_TQ 表

### 7. 数据提取

配置完成后，您可以使用：

- `daily_meteorological_extractor.py` - 主要的数据提取工具
- 它会自动读取 `sql_connection_config.py` 中的连接配置

## 故障排除

### 常见错误及解决方法

**错误**: "未发现数据源名称"
- **原因**: 连接字符串格式错误
- **解决**: 使用标准的连接字符串格式

**错误**: "登录超时已过期"
- **原因**: 网络连接问题或服务器负载高
- **解决**: 检查网络连接，增加连接超时时间

**错误**: "无法连接到服务器"
- **原因**: 服务器地址错误或服务器未运行
- **解决**: 验证服务器地址和端口

**错误**: "登录失败"
- **原因**: 用户名******错误或权限不足
- **解决**: 检查账户信息和数据库权限

## 下一步操作

1. 完成 `remote_sql_setup_clean.py` 的交互式配置
2. 确认连接测试成功
3. 运行数据提取脚本测试单日数据
4. 如果成功，开始批量提取2024年数据

## 联系信息

如果您需要帮助确定服务器信息，请提供：

- 您通常如何访问这个数据库？
- 是否有其他应用程序连接同一个数据库？
- 您是否有数据库管理员的联系方式？
