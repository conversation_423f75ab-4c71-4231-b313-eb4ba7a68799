#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据日度提取器
使用Python循环提取每天的气象站数据并保存为CSV文件
避免一次性提取大量数据的问题
"""

import pandas as pd
import pyodbc
from datetime import datetime, timedelta
import os
from pathlib import Path
import logging
import time
import json
from typing import List, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_extraction.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DailyMeteorologicalExtractor:
    """日度气象数据提取器"""
    
    def __init__(self, connection_string: str = None, output_dir: str = None):
        """
        初始化提取器
        
        Args:
            connection_string: 数据库连接字符串
            output_dir: 输出目录
        """
        self.connection_string = connection_string
        self.output_dir = Path(output_dir) if output_dir else Path("f:/HRCLDAS/daily_csv_data")
        self.output_dir.mkdir(exist_ok=True)
        
        # 313个气象站点ID列表
        self.station_ids = [
            'N0435', 'N1003', 'N1006', 'N1009', 'N1010', 'N1013', 'N1014', 'N1015', 'N1016', 'N1017',
            'N1018', 'N1019', 'N1020', 'N1023', 'N1024', 'N1025', 'N1026', 'N1027', 'N1028', 'N1029',
            'N1033', 'N1034', 'N1035', 'N1036', 'N1037', 'N1038', 'N1039', 'N1040', 'N1043', 'N1044',
            'N1045', 'N1046', 'N1047', 'N1048', 'N1049', 'N1050', 'N1053', 'N1054', 'N1055', 'N1056',
            'N1057', 'N1058', 'N1059', 'N1060', 'N1063', 'N1064', 'N1065', 'N1066', 'N1067', 'N1068',
            'N1069', 'N1070', 'N1073', 'N1074', 'N1075', 'N1076', 'N1077', 'N1078', 'N1079', 'N1083',
            'N1085', 'N1086', 'N1087', 'N1088', 'N1089', 'N1090', 'N1093', 'N1095', 'N1096', 'N1097',
            'N1098', 'N1099', 'N1103', 'N1104', 'N1105', 'N1106', 'N1107', 'N1108', 'N1109', 'N1113',
            'N1114', 'N1115', 'N1116', 'N1117', 'N1118', 'N1119', 'N1120', 'N1123', 'N1124', 'N1125',
            'N1128', 'N1129', 'N1130', 'N1133', 'N1134', 'N1135', 'N1137', 'N1138', 'N1139', 'N1140',
            'N1143', 'N1144', 'N1145', 'N1147', 'N1148', 'N1150', 'N1153', 'N1154', 'N1155', 'N1157',
            'N1158', 'N1159', 'N1160', 'N1163', 'N1164', 'N1165', 'N1167', 'N1168', 'N1169', 'N1170',
            'N1173', 'N1174', 'N1175', 'N1178', 'N1179', 'N1180', 'N1183', 'N1184', 'N1185', 'N1187',
            'N1188', 'N1189', 'N1190', 'N1193', 'N1194', 'N1195', 'N1198', 'N1199', 'N1200', 'N1203',
            'N1204', 'N1205', 'N1208', 'N1209', 'N1210', 'N1213', 'N1214', 'N1215', 'N1218', 'N1219',
            'N1223', 'N1224', 'N1225', 'N1227', 'N1228', 'N1229', 'N1230', 'N1233', 'N1234', 'N1235',
            'N1237', 'N1238', 'N1239', 'N1240', 'N1243', 'N1244', 'N1245', 'N1247', 'N1248', 'N1249',
            'N1250', 'N1253', 'N1254', 'N1255', 'N1258', 'N1259', 'N1260', 'N1263', 'N1264', 'N1265',
            'N1268', 'N1269', 'N1273', 'N1274', 'N1275', 'N1277', 'N1278', 'N1279', 'N1280', 'N1283',
            'N1284', 'N1285', 'N1287', 'N1289', 'N1290', 'N1293', 'N1294', 'N1295', 'N1297', 'N1299',
            'N1300', 'N1303', 'N1304', 'N1307', 'N1308', 'N1309', 'N1310', 'N1313', 'N1314', 'N1315',
            'N1317', 'N1319', 'N1320', 'N1323', 'N1324', 'N1325', 'N1327', 'N1329', 'N1330', 'N1333',
            'N1334', 'N1335', 'N1337', 'N1339', 'N1340', 'N1343', 'N1344', 'N1345', 'N1347', 'N1353',
            'N1354', 'N1357', 'N1360', 'N1363', 'N1364', 'N1365', 'N1367', 'N1370', 'N1373', 'N1374',
            'N1377', 'N1380', 'N1384', 'N1387', 'N1390', 'N1393', 'N1394', 'N1397', 'N1400', 'N1403',
            'N1407', 'N1409', 'N1410', 'N1413', 'N1417', 'N1419', 'N1420', 'N1423', 'N1427', 'N1429',
            'N1430', 'N1433', 'N1437', 'N1440', 'N1443', 'N1447', 'N1450', 'N1453', 'N1457', 'N1460',
            'N1463', 'N1467', 'N1470', 'N1473', 'N1477', 'N1480', 'N1487', 'N1490', 'N1497', 'N1500',
            'N1507', 'N1510', 'N1517', 'N1520', 'N1527', 'N1530', 'N1540', 'N1550', 'N1560', 'N1570',
            'N1580', 'N1590', 'N1600', 'N1610', 'N1620', 'N1630', 'N1640', 'N1650', 'N1660', 'N1670',
            'N1680', 'N1690', 'N1700', 'N1710', 'N1730', 'N1740', 'N1750', 'N1760', 'N1770', 'N1780',
            'N1790', 'N1800', 'N1840'
        ]
        
        logger.info(f"初始化日度提取器，共 {len(self.station_ids)} 个气象站点")
        logger.info(f"输出目录: {self.output_dir}")
    
    def generate_daily_sql(self, target_date: str) -> str:
        """
        生成指定日期的SQL查询
        
        Args:
            target_date: 目标日期，格式为 'YYYY-MM-DD'
            
        Returns:
            SQL查询字符串
        """
        station_list = "', '".join(self.station_ids)
        
        sql = f"""
SELECT t.[Station_Id_c] AS 站点ID
      ,t.[DATETIME] AS 观测时间
      ,t.[Lat] AS 纬度
      ,t.[Lon] AS 经度
      ,t.[Alti] AS 海拔
      ,t.[V_ACODE] AS 观测代码
      ,t.[PRS] AS 气压_hPa
      ,t.[PRS_Sea] AS 海平面气压_hPa
      ,t.[PRS_Max] AS 最高气压_hPa
      ,t.[PRS_Min] AS 最低气压_hPa
      ,t.[TEM] AS 温度_C
      ,t.[TEM_Max] AS 最高温度_C
      ,t.[TEM_Min] AS 最低温度_C
      ,t.[DPT] AS 露点温度_C
      ,t.[RHU] AS 相对湿度_percent
      ,t.[RHU_Min] AS 最小相对湿度_percent
      ,t.[VAP] AS 水汽压_hPa
      ,t.[PRE_1h] AS 小时降水量_mm
      ,t.[WIN_D_Avg_2mi] AS 平均风向2分钟_degree
      ,t.[WIN_S_Avg_2mi] AS 平均风速2分钟_ms
      ,t.[WIN_D_Avg_10mi] AS 平均风向10分钟_degree
      ,t.[WIN_S_Avg_10mi] AS 平均风速10分钟_ms
      ,t.[WIN_D_S_Max] AS 最大风速风向_degree
      ,t.[WIN_S_Max] AS 最大风速_ms
      ,t.[WIN_D_INST] AS 瞬时风向_degree
      ,t.[WIN_S_INST] AS 瞬时风速_ms
      ,t.[VIS_HOR_1MI] AS 水平能见度1分钟_m
      ,t.[VIS_HOR_10MI] AS 水平能见度10分钟_m
      ,t.[VIS_Min] AS 最小能见度_m
      ,t.[Q_PRE_1h] AS 降水量质量码
      ,t.[Q_TEM_Min] AS 最低温度质量码
      ,t.[Q_TEM] AS 温度质量码
      ,t.[Q_TEM_Max] AS 最高温度质量码
FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ] t
WHERE t.[Station_Id_c] IN ('{station_list}')
  AND CAST(t.[DATETIME] AS DATE) = '{target_date}'
ORDER BY t.[Station_Id_c], t.[DATETIME];
"""
        return sql
    
    def extract_daily_data(self, target_date: str) -> Optional[pd.DataFrame]:
        """
        提取指定日期的数据
        
        Args:
            target_date: 目标日期，格式为 'YYYY-MM-DD'
            
        Returns:
            查询结果DataFrame，如果失败返回None
        """
        if not self.connection_string:
            logger.error("未设置数据库连接字符串")
            return None
        
        sql = self.generate_daily_sql(target_date)
        
        try:
            with pyodbc.connect(self.connection_string) as conn:
                df = pd.read_sql(sql, conn)
                logger.info(f"日期 {target_date}: 提取到 {len(df)} 条记录")
                return df
                
        except Exception as e:
            logger.error(f"提取日期 {target_date} 数据失败: {e}")
            return None
    
    def save_daily_csv(self, df: pd.DataFrame, target_date: str) -> bool:
        """
        保存日期数据为CSV文件
        
        Args:
            df: 要保存的DataFrame
            target_date: 日期字符串
            
        Returns:
            是否保存成功
        """
        try:
            # 生成文件名
            date_str = target_date.replace('-', '')
            filename = f"meteorological_{date_str}.csv"
            filepath = self.output_dir / filename
            
            # 保存文件
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"保存成功: {filename} ({len(df)} 条记录)")
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败 {target_date}: {e}")
            return False
    
    def extract_date_range(self, start_date: str, end_date: str, 
                          delay_seconds: float = 1.0) -> dict:
        """
        提取日期范围内的所有数据
        
        Args:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            delay_seconds: 每次查询间隔秒数
            
        Returns:
            提取结果统计
        """
        logger.info(f"开始提取日期范围: {start_date} 到 {end_date}")
        
        # 解析日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 统计信息
        stats = {
            "总天数": 0,
            "成功天数": 0,
            "失败天数": 0,
            "总记录数": 0,
            "成功文件": [],
            "失败日期": [],
            "开始时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 逐日提取
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            stats["总天数"] += 1
            
            logger.info(f"处理日期: {date_str} ({stats['总天数']}/{(end_dt - start_dt).days + 1})")
            
            # 提取数据
            df = self.extract_daily_data(date_str)
            
            if df is not None and len(df) > 0:
                # 保存数据
                if self.save_daily_csv(df, date_str):
                    stats["成功天数"] += 1
                    stats["总记录数"] += len(df)
                    stats["成功文件"].append(f"meteorological_{date_str.replace('-', '')}.csv")
                else:
                    stats["失败天数"] += 1
                    stats["失败日期"].append(date_str)
            else:
                logger.warning(f"日期 {date_str} 无数据或提取失败")
                stats["失败天数"] += 1
                stats["失败日期"].append(date_str)
            
            # 移到下一天
            current_date += timedelta(days=1)
            
            # 延迟，避免数据库压力过大
            if delay_seconds > 0:
                time.sleep(delay_seconds)
        
        stats["结束时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 保存统计信息
        self.save_extraction_summary(stats)
        
        return stats
    
    def save_extraction_summary(self, stats: dict):
        """保存提取摘要"""
        try:
            summary_file = self.output_dir / "extraction_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            logger.info(f"提取摘要已保存: {summary_file}")
            
            # 打印摘要
            print("\n" + "="*60)
            print("📊 提取完成摘要")
            print("="*60)
            print(f"📅 时间范围: {stats['开始时间']} - {stats['结束时间']}")
            print(f"📈 总天数: {stats['总天数']}")
            print(f"✅ 成功: {stats['成功天数']} 天")
            print(f"❌ 失败: {stats['失败天数']} 天")
            print(f"📊 总记录数: {stats['总记录数']:,}")
            print(f"📄 生成文件: {len(stats['成功文件'])} 个")
            
            if stats['失败日期']:
                print(f"⚠️  失败日期: {', '.join(stats['失败日期'][:10])}")
                if len(stats['失败日期']) > 10:
                    print(f"    ... 还有 {len(stats['失败日期']) - 10} 个")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"保存摘要失败: {e}")
    
    def extract_year_2024(self, delay_seconds: float = 1.0) -> dict:
        """
        提取2024年全年数据
        
        Args:
            delay_seconds: 每次查询间隔秒数
            
        Returns:
            提取结果统计
        """
        return self.extract_date_range('2024-01-01', '2024-12-31', delay_seconds)
    
    def resume_extraction(self, start_date: str, end_date: str, 
                         delay_seconds: float = 1.0) -> dict:
        """
        从已有文件的基础上继续提取（跳过已存在的文件）
        
        Args:
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            delay_seconds: 每次查询间隔秒数
            
        Returns:
            提取结果统计
        """
        logger.info("检查已存在的文件...")
        
        # 获取已存在的文件
        existing_files = set()
        for file in self.output_dir.glob("meteorological_*.csv"):
            # 从文件名提取日期
            date_part = file.stem.replace('meteorological_', '')
            if len(date_part) == 8 and date_part.isdigit():
                date_str = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"
                existing_files.add(date_str)
        
        logger.info(f"找到 {len(existing_files)} 个已存在的文件")
        
        # 解析日期范围
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 找出需要提取的日期
        dates_to_extract = []
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            if date_str not in existing_files:
                dates_to_extract.append(date_str)
            current_date += timedelta(days=1)
        
        logger.info(f"需要提取 {len(dates_to_extract)} 个日期的数据")
        
        if not dates_to_extract:
            logger.info("所有文件都已存在，无需提取")
            return {"message": "所有文件都已存在"}
        
        # 提取缺失的日期
        stats = {
            "总天数": len(dates_to_extract),
            "成功天数": 0,
            "失败天数": 0,
            "总记录数": 0,
            "成功文件": [],
            "失败日期": [],
            "跳过文件数": len(existing_files),
            "开始时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        for i, date_str in enumerate(dates_to_extract, 1):
            logger.info(f"处理日期: {date_str} ({i}/{len(dates_to_extract)})")
            
            df = self.extract_daily_data(date_str)
            
            if df is not None and len(df) > 0:
                if self.save_daily_csv(df, date_str):
                    stats["成功天数"] += 1
                    stats["总记录数"] += len(df)
                    stats["成功文件"].append(f"meteorological_{date_str.replace('-', '')}.csv")
                else:
                    stats["失败天数"] += 1
                    stats["失败日期"].append(date_str)
            else:
                stats["失败天数"] += 1
                stats["失败日期"].append(date_str)
            
            if delay_seconds > 0:
                time.sleep(delay_seconds)
        
        stats["结束时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.save_extraction_summary(stats)
        
        return stats


def detect_sql_server_drivers():
    """检测可用的SQL Server驱动程序"""
    try:
        available_drivers = pyodbc.drivers()
        sql_drivers = [d for d in available_drivers if 'SQL Server' in d]
        return sql_drivers
    except:
        return []

def setup_database_connection() -> str:
    """设置数据库连接字符串"""
    print("数据库连接设置")
    print("="*50)
    
    # 检测可用驱动程序
    print("🔍 检测SQL Server驱动程序...")
    drivers = detect_sql_server_drivers()
    
    if drivers:
        print("✅ 找到以下SQL Server驱动程序:")
        for i, driver in enumerate(drivers, 1):
            print(f"  {i}. {driver}")
        print()
    else:
        print("⚠️  未找到SQL Server驱动程序")
        print("请确保已安装以下驱动程序之一:")
        print("  - ODBC Driver 17 for SQL Server")
        print("  - ODBC Driver 13 for SQL Server") 
        print("  - SQL Server Native Client")
        print("  - SQL Server")
        print()
    
    # 驱动程序选择
    driver_options = [
        "ODBC Driver 17 for SQL Server",
        "ODBC Driver 13 for SQL Server", 
        "ODBC Driver 11 for SQL Server",
        "SQL Server Native Client 11.0",
        "SQL Server Native Client 10.0",
        "SQL Server"
    ]
    
    print("请选择驱动程序:")
    for i, driver in enumerate(driver_options, 1):
        status = "✅" if driver in drivers else "❌"
        print(f"  {i}. {driver} {status}")
    print(f"  {len(driver_options)+1}. 手动输入驱动程序名称")
    
    driver_choice = input(f"\n请选择驱动程序 (1-{len(driver_options)+1}): ").strip()
    
    try:
        choice_num = int(driver_choice)
        if 1 <= choice_num <= len(driver_options):
            selected_driver = driver_options[choice_num - 1]
        elif choice_num == len(driver_options) + 1:
            selected_driver = input("请输入驱动程序名称: ").strip()
        else:
            print("无效选择，使用默认驱动程序")
            selected_driver = "SQL Server"
    except:
        print("无效输入，使用默认驱动程序")
        selected_driver = "SQL Server"
    
    print(f"📌 选择的驱动程序: {selected_driver}")
    
    # 连接方式选择
    print("\n请选择连接方式:")
    print("1. Windows身份验证 (推荐)")
    print("2. SQL Server身份验证")
    print("3. 手动输入完整连接字符串")
    
    auth_choice = input("请选择 (1-3): ").strip()
    
    if auth_choice == "1":
        server = input("请输入服务器名称 (默认: localhost): ").strip() or "localhost"
        database = input("请输入数据库名称 (默认: SURF_DAAS): ").strip() or "SURF_DAAS"
        conn_str = f"Driver={{{selected_driver}}};Server={server};Database={database};Trusted_Connection=yes;"
        
    elif auth_choice == "2":
        server = input("请输入服务器名称 (默认: localhost): ").strip() or "localhost"
        database = input("请输入数据库名称 (默认: SURF_DAAS): ").strip() or "SURF_DAAS"
        username = input("请输入用户名: ").strip()
        password = input("请输入密码: ").strip()
        conn_str = f"Driver={{{selected_driver}}};Server={server};Database={database};UID={username};PWD={password};"
        
    elif auth_choice == "3":
        conn_str = input("请输入完整的连接字符串: ").strip()
        
    else:
        print("无效选择，使用默认连接")
        conn_str = f"Driver={{{selected_driver}}};Server=localhost;Database=SURF_DAAS;Trusted_Connection=yes;"
    
    print(f"\n🔗 连接字符串: {conn_str}")
    
    # 测试连接
    test_conn = input("\n是否测试连接? (y/n, 默认y): ").strip().lower()
    if test_conn != 'n':
        print("🧪 测试数据库连接...")
        try:
            test_connection = pyodbc.connect(conn_str, timeout=10)
            test_connection.close()
            print("✅ 连接测试成功!")
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            print("请检查:")
            print("  1. 服务器名称是否正确")
            print("  2. 数据库名称是否正确") 
            print("  3. 网络连接是否正常")
            print("  4. SQL Server服务是否启动")
            print("  5. 防火墙设置是否正确")
            
            retry = input("\n是否重新配置连接? (y/n): ").strip().lower()
            if retry == 'y':
                return setup_database_connection()
    
    return conn_str


def main():
    """主函数"""
    print("🌤️  气象数据日度提取器")
    print("="*60)
    
    # 设置数据库连接
    print("\n📡 数据库连接设置")
    connection_string = setup_database_connection()
    
    # 设置输出目录
    output_dir = input(f"\n📁 输出目录 (回车使用默认: f:/HRCLDAS/daily_csv_data): ").strip()
    if not output_dir:
        output_dir = "f:/HRCLDAS/daily_csv_data"
    
    # 创建提取器
    extractor = DailyMeteorologicalExtractor(connection_string, output_dir)
    
    # 选择提取模式
    print(f"\n📋 提取模式选择:")
    print("1. 提取2024年全年数据")
    print("2. 提取指定日期范围")
    print("3. 继续未完成的提取")
    print("4. 提取单个日期")
    
    mode = input("请选择 (1-4): ").strip()
    
    try:
        if mode == "1":
            print("\n🚀 开始提取2024年全年数据...")
            delay = float(input("查询间隔秒数 (默认1秒): ").strip() or "1")
            stats = extractor.extract_year_2024(delay)
            
        elif mode == "2":
            start_date = input("开始日期 (YYYY-MM-DD): ").strip()
            end_date = input("结束日期 (YYYY-MM-DD): ").strip()
            delay = float(input("查询间隔秒数 (默认1秒): ").strip() or "1")
            stats = extractor.extract_date_range(start_date, end_date, delay)
            
        elif mode == "3":
            start_date = input("开始日期 (YYYY-MM-DD, 默认2024-01-01): ").strip() or "2024-01-01"
            end_date = input("结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip() or "2024-12-31"
            delay = float(input("查询间隔秒数 (默认1秒): ").strip() or "1")
            stats = extractor.resume_extraction(start_date, end_date, delay)
            
        elif mode == "4":
            target_date = input("目标日期 (YYYY-MM-DD): ").strip()
            df = extractor.extract_daily_data(target_date)
            if df is not None:
                extractor.save_daily_csv(df, target_date)
                print(f"✅ 单日提取完成: {target_date}")
            else:
                print(f"❌ 单日提取失败: {target_date}")
            return
            
        else:
            print("❌ 无效选择")
            return
        
        print(f"\n🎉 提取任务完成!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断提取")
    except Exception as e:
        print(f"\n❌ 提取过程出错: {e}")
        logger.exception("详细错误信息")


if __name__ == "__main__":
    main()
