#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入必要的库
import os
import re
import glob
import warnings
import traceback
from datetime import datetime, timedelta
from collections import defaultdict
import xarray as xr
import numpy as np
import logging
import pygrib
import time
from multiprocessing import cpu_count
from tqdm.notebook import tqdm
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# 配置警告过滤
warnings.filterwarnings("ignore", category=FutureWarning)

# 配置日志和警告 (保持原有配置)
logging.getLogger('cfgrib').setLevel(logging.ERROR)
logging.getLogger('eccodes').setLevel(logging.ERROR)
logging.getLogger('cfgrib').setLevel(logging.CRITICAL)
warnings.filterwarnings("ignore", category=RuntimeWarning, module="cfgrib")

class TimestampedLogger:
    """带时间戳的日志记录器(支持级别控制)"""
    def __init__(self, log_file=None):
        self.log_file = log_file
        if log_file:
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("")
        self.operation_start_time = {}

    def log_header(self, source_dir, grib_dir, output_dir, time_range):
        """记录头部信息"""
        message = f"""===== GRIB数据处理程序 (适配版) =====
源目录: {source_dir}
GRIB目录: {grib_dir}
输出目录: {output_dir}
时间范围: {time_range}
日志文件: {self.log_file if self.log_file else '无'}
{"=" * 40}

"""
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message)

    def start_operation(self, operation_name):
        """记录操作开始时间"""
        self.operation_start_time[operation_name] = time.time()

    def log_operation(self, operation_name, status_message, level='INFO'):
        """根据级别记录操作结果"""
        if operation_name not in self.operation_start_time:
            self.start_operation(operation_name)
        
        elapsed = time.time() - self.operation_start_time.get(operation_name, time.time())
        
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        message = f"{timestamp} {operation_name.ljust(12)}{elapsed:.2f}s\t{status_message}"
        
        # 始终写入日志文件
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")
        
        # 仅当级别为'ERROR'时在控制台打印
        if level == 'ERROR':
            print(message)
        
        if operation_name in self.operation_start_time:
            del self.operation_start_time[operation_name]

    def log_footer(self, success=True):
        """记录结束信息"""
        message = "\n===== 处理完成 ====="
        if not success:
            message += "\n===== 处理过程中出现错误 ====="
        
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(message + "\n")

class ECMWFProcessorAdapted:
    """适配新GRIB2格式的ECMWF处理器 - 保持原有逻辑结构"""

    def __init__(self, log_file=None):
        # 初始化日志记录器 (保持原有)
        self.logger = TimestampedLogger(log_file)
        
        # 通用转换器 - 不预定义变量列表，接受所有变量
        # 只保留原有的变量映射用于重命名（可选）
        
        # 可选的变量重命名映射 (如果不想重命名，可以设为空字典)
        # 设为空字典表示保持原始变量名
        self.GRIB_TO_NC_VAR_MAP = {
            # 如果需要重命名特定变量，可以在这里添加
            # 例如: '2r': 'rh2m', '2t': 't2m'
            # 现在设为空，保持所有原始变量名
        }
        
        # 保持原有的预报时效分类 (但新格式可能不需要)
        self.TYPE1_FORECAST_HOURS = [
            6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96, 
            102, 108, 114, 120, 126, 132, 138, 144
        ]
        
        # 新格式通常是单时次文件，添加0时效支持
        self.NEW_FORMAT_FORECAST_HOURS = [0]  # 实况或分析场
        
        # 保持原有的其他配置
        self.metadata_cache = {}
        self.MAX_WORKERS = min(32, max(1, cpu_count() - 2))

# ===== ECMWFProcessorAdapted 核心处理方法 =====

def process_new_format_file(self, input_file, output_dir, reference_time=None):
    """处理新格式的单个GRIB2文件 - 保持原有转换逻辑"""
    self.logger.start_operation("处理文件")
    
    try:
        # 如果没有提供参考时间，从文件名推断
        if reference_time is None:
            reference_time = self._parse_new_format_time(input_file)
        
        # 使用原有的数据读取和转换逻辑
        surface_ds, upper_ds = self._process_single_grib_file_new_format(input_file)
        
        if surface_ds is None and upper_ds is None:
            # 记录为错误，但不在控制台显示
            self.logger.log_operation("处理文件", f"{os.path.basename(input_file)} -> 未提取到任何数据", level='ERROR')
            return False
        
        # 获取原始文件名（不含扩展名）
        original_filename = os.path.splitext(os.path.basename(input_file))[0]
        os.makedirs(output_dir, exist_ok=True)
        
        ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M") if isinstance(reference_time, str) else reference_time
        
        # 合并数据集（如果需要）
        if surface_ds is not None and upper_ds is not None:
            combined_ds = xr.merge([surface_ds, upper_ds])
        elif surface_ds is not None:
            combined_ds = surface_ds
        else:
            combined_ds = upper_ds

        # 统一保存到一个 .nc 文件
        output_path = os.path.join(output_dir, f"{original_filename}.nc")
        self._save_dataset_original_format(combined_ds, output_path, ref_dt)
        
        # 成功信息只写入日志
        self.logger.log_operation("处理文件", f"{os.path.basename(input_file)} -> {os.path.basename(output_path)}", level='INFO')
        return True

    except Exception as e:
        # 失败信息写入日志并打印到控制台
        self.logger.log_operation("处理文件", f"{os.path.basename(input_file)} -> 失败: {str(e)}", level='ERROR')
        return False

def _parse_new_format_time(self, file_path):
    """解析新格式文件名中的时间信息"""
    filename = os.path.basename(file_path)
    # 新格式: Z_NAFP_C_BABJ_20240101000535_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024010100.GRB2
    pattern = r'Z_\w+_C_\w+_(\d{14})_P_\w+_\w+_\w+_\w+_\w+-\w+-(\d{10})\.GRB2?'
    match = re.search(pattern, filename)

    if match:
        data_time_str = match.group(2)  # YYYYMMDDHH
        try:
            data_time = datetime.strptime(data_time_str, "%Y%m%d%H")
            return data_time.strftime("%Y%m%d%H%M")  # 转换为原有格式
        except ValueError:
            pass
    # 如果解析失败，使用当前时间
    return datetime.now().strftime("%Y%m%d%H%M")

def _process_single_grib_file_new_format(self, grib_file):
    """处理新格式的单个GRIB文件 - 保持原有数据读取逻辑"""
    try:
        surface_ds = None
        upper_ds = None

        # 通用读取 - 尝试读取所有数据，然后按层次类型分类
        try:
            all_ds = xr.open_dataset(grib_file, engine="cfgrib")
            surface_vars = []
            upper_vars = []

            for var_name in all_ds.data_vars:
                var = all_ds[var_name]
                if 'isobaricInhPa' in var.dims:
                    upper_vars.append(var_name)
                else:
                    surface_vars.append(var_name)

            if surface_vars:
                surface_ds = all_ds[surface_vars]
            else:
                surface_ds = None

        except Exception as e:
            # 备用方案：分别读取地面和高空数据
            try:
                surface_ds = xr.open_dataset(
                    grib_file,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': ['surface', 'heightAboveGround', 'meanSea']
                        }
                    }
                )
                if not surface_ds.data_vars:
                    surface_ds = None
            except Exception as e2:
                surface_ds = None

        # 读取高空数据
        try:
            if 'all_ds' in locals() and upper_vars:
                upper_ds = all_ds[upper_vars]
            else:
                upper_ds = xr.open_dataset(
                    grib_file,
                    engine="cfgrib",
                    backend_kwargs={
                        'filter_by_keys': {
                            'typeOfLevel': 'isobaricInhPa'
                        }
                    }
                )
                if not upper_ds.data_vars:
                    upper_ds = None
        except Exception as e:
            upper_ds = None

        # 如果cfgrib完全失败，使用pygrib备用方案
        if surface_ds is None and upper_ds is None:
            surface_ds, upper_ds = self._read_with_pygrib_backup(grib_file)

        # 可选的变量重命名 (如果映射表为空则跳过)
        if surface_ds is not None:
            if self.GRIB_TO_NC_VAR_MAP:
                rename_dict = {}
                for var in surface_ds.data_vars:
                    if var in self.GRIB_TO_NC_VAR_MAP:
                        rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
                if rename_dict:
                    surface_ds = surface_ds.rename(rename_dict)
            self._add_coordinate_attributes(surface_ds)

        if upper_ds is not None:
            if self.GRIB_TO_NC_VAR_MAP:
                rename_dict = {}
                for var in upper_ds.data_vars:
                    if var in self.GRIB_TO_NC_VAR_MAP:
                        rename_dict[var] = self.GRIB_TO_NC_VAR_MAP[var]
                if rename_dict:
                    upper_ds = upper_ds.rename(rename_dict)
            self._add_coordinate_attributes(upper_ds)

        return surface_ds, upper_ds

    except Exception as e:
        self.logger.log_operation("处理GRIB文件", f"失败: {e}")
        return None, None

def _read_with_pygrib_backup(self, grib_file):
    """使用pygrib的备用读取方案"""
    try:
        with pygrib.open(grib_file) as grbs:
            messages = list(grbs)
            if not messages:
                return None, None

            # 获取网格信息
            first_msg = messages[0]
            lats, lons = first_msg.latlons()
            lat_1d = lats[:, 0]
            lon_1d = lons[0, :]

            # 分类数据
            surface_data = {}
            upper_data = defaultdict(dict)

            for msg in messages:
                var_name = msg.shortName
                level_type = msg.typeOfLevel
                level_value = msg.level
                data = msg.values

                da = xr.DataArray(
                    data,
                    dims=['latitude', 'longitude'],
                    coords={
                        'latitude': xr.DataArray(lat_1d, dims=['latitude'], attrs={'units': 'degrees_north', 'long_name': 'latitude', 'standard_name': 'latitude'}),
                        'longitude': xr.DataArray(lon_1d, dims=['longitude'], attrs={'units': 'degrees_east', 'long_name': 'longitude', 'standard_name': 'longitude'})
                    },
                    attrs={'long_name': msg.name, 'units': msg.units, 'level_type': level_type, 'level': level_value}
                )

                if level_type in ['surface', 'heightAboveGround', 'meanSea']:
                    surface_data[var_name] = da
                elif level_type == 'isobaricInhPa':
                    upper_data[level_value][var_name] = da
                else:
                    surface_data[var_name] = da

            # 创建Dataset
            surface_ds = None
            upper_ds = None

            if surface_data:
                surface_ds = xr.Dataset(surface_data)
                surface_ds = surface_ds.expand_dims({'level': [0]})

            if upper_data:
                upper_datasets = []
                for level in sorted(upper_data.keys(), reverse=True):
                    level_ds = xr.Dataset(upper_data[level])
                    level_ds = level_ds.expand_dims({'isobaricInhPa': [level]})
                    upper_datasets.append(level_ds)
                if upper_datasets:
                    upper_ds = xr.concat(upper_datasets, dim='isobaricInhPa')

            return surface_ds, upper_ds

    except Exception as e:
        self.logger.log_operation("pygrib读取", f"失败: {e}", level='ERROR')
        return None, None

def _add_coordinate_attributes(self, dataset):
    """为数据集添加完整的坐标属性"""
    if 'latitude' in dataset.coords:
        dataset.coords['latitude'].attrs.update({'units': 'degrees_north', 'long_name': 'latitude', 'standard_name': 'latitude', 'axis': 'Y'})
    if 'longitude' in dataset.coords:
        dataset.coords['longitude'].attrs.update({'units': 'degrees_east', 'long_name': 'longitude', 'standard_name': 'longitude', 'axis': 'X'})
    if 'isobaricInhPa' in dataset.coords:
        dataset.coords['isobaricInhPa'].attrs.update({'units': 'hPa', 'long_name': 'pressure', 'standard_name': 'air_pressure', 'axis': 'Z', 'positive': 'down'})
    if 'time' in dataset.coords:
        dataset.coords['time'].attrs.update({'long_name': 'time', 'standard_name': 'time', 'axis': 'T'})
    if 'level' in dataset.coords:
        dataset.coords['level'].attrs.update({'long_name': 'level', 'units': '1'})

def _save_dataset_original_format(self, dataset, output_path, ref_time):
    """保存数据集 - 完全保持原有格式和逻辑，增加地理坐标系信息"""
    try:
        if 'time' not in dataset.coords:
            dataset = dataset.expand_dims('time')
            dataset.coords['time'] = [ref_time]

        dataset.coords['reference_time'] = np.datetime64(ref_time, 'ns')
        self._add_coordinate_attributes(dataset)

        dataset.attrs.update({
            'modename': 'HRCLDAS_ADAPTED',
            'description': 'Adapted HRCLDAS data processed with original ECMWF logic',
            'creation_time': datetime.now().isoformat(),
            'source_file': 'HRCLDAS GRIB2 file',
            'Conventions': 'CF-1.6',
            'coordinate_system': 'WGS84',
            'geospatial_lat_min': float(dataset.latitude.min()),
            'geospatial_lat_max': float(dataset.latitude.max()),
            'geospatial_lon_min': float(dataset.longitude.min()),
            'geospatial_lon_max': float(dataset.longitude.max()),
            'geospatial_lat_units': 'degrees_north',
            'geospatial_lon_units': 'degrees_east'
        })

        encoding = {}
        for var in dataset.data_vars:
            encoding[var] = {
                'zlib': True,
                'complevel': 1,
                'dtype': 'float32',
                '_FillValue': None,
                'chunksizes': self._get_chunksizes_original(dataset[var])
            }

        encoding.update({
            'time': {'dtype': 'int64', '_FillValue': None},
            'reference_time': {'dtype': 'int64', '_FillValue': None}
        })

        dataset.to_netcdf(output_path, encoding=encoding)
    except Exception as e:
        raise

def _get_chunksizes_original(self, var):
    """计算变量的合适chunk大小 - 保持原有逻辑"""
    chunks = []
    for dim in var.dims:
        size = len(var[dim])
        if dim == 'isobaricInhPa':
            chunks.append(min(4, size))
        elif dim == 'latitude':
            chunks.append(min(128, size))
        elif dim == 'longitude':
            chunks.append(min(256, size))
        elif dim in ['time', 'forecast_time']:
            chunks.append(1)
        else:
            chunks.append(size)
    return tuple(chunks)

# 将这些方法添加到ECMWFProcessorAdapted类中
ECMWFProcessorAdapted.process_new_format_file = process_new_format_file
ECMWFProcessorAdapted._parse_new_format_time = _parse_new_format_time
ECMWFProcessorAdapted._process_single_grib_file_new_format = _process_single_grib_file_new_format
ECMWFProcessorAdapted._read_with_pygrib_backup = _read_with_pygrib_backup
ECMWFProcessorAdapted._add_coordinate_attributes = _add_coordinate_attributes
ECMWFProcessorAdapted._save_dataset_original_format = _save_dataset_original_format
ECMWFProcessorAdapted._get_chunksizes_original = _get_chunksizes_original

# ===== 批量处理和时间序列处理方法 (多核心版本) =====

def process_single_file_worker(args):
    """用于多进程的单文件处理工作函数"""
    input_file, output_dir, log_file = args
    
    # 为每个进程创建独立的处理器实例
    from datetime import datetime
    import os
    import xarray as xr
    import numpy as np
    import re
    import pygrib
    from collections import defaultdict
    
    try:
        # 创建临时处理器 (不使用日志文件避免冲突)
        processor = ECMWFProcessorAdapted()
        
        # 解析时间
        reference_time = processor._parse_new_format_time(input_file)
        
        # 处理文件
        surface_ds, upper_ds = processor._process_single_grib_file_new_format(input_file)
        
        if surface_ds is None and upper_ds is None:
            return False, f"{os.path.basename(input_file)} -> 未提取到任何数据"
        
        # 获取原始文件名（不含扩展名）
        original_filename = os.path.splitext(os.path.basename(input_file))[0]
        os.makedirs(output_dir, exist_ok=True)
        
        ref_dt = datetime.strptime(reference_time, "%Y%m%d%H%M") if isinstance(reference_time, str) else reference_time
        
        # 合并数据集
        if surface_ds is not None and upper_ds is not None:
            combined_ds = xr.merge([surface_ds, upper_ds])
        elif surface_ds is not None:
            combined_ds = surface_ds
        else:
            combined_ds = upper_ds

        # 保存文件
        output_path = os.path.join(output_dir, f"{original_filename}.nc")
        processor._save_dataset_original_format(combined_ds, output_path, ref_dt)
        
        return True, f"{os.path.basename(input_file)} -> {os.path.basename(output_path)}"
        
    except Exception as e:
        return False, f"{os.path.basename(input_file)} -> 失败: {str(e)}"

def batch_process_new_format_directory(self, input_dir, output_dir, pattern="*.GRB2", use_multiprocessing=True, target_variables=None, recursive=True):
    """批量处理新格式文件目录 - 多核心并行版本，支持变量过滤和递归搜索
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        pattern: 文件匹配模式
        use_multiprocessing: 是否使用多进程
        target_variables: 目标变量列表，如 ['TAIR', 'QAIR']，None表示处理所有文件
        recursive: 是否递归搜索子文件夹
    """
    self.logger.start_operation("批量处理")

    # 递归查找文件
    if recursive:
        all_files = self._find_files_recursive(input_dir, pattern)
        print(f"🔍 递归搜索模式: 在 {input_dir} 及其子文件夹中搜索")
    else:
        search_pattern = os.path.join(input_dir, pattern)
        all_files = glob.glob(search_pattern)
        print(f"📁 单层搜索模式: 仅在 {input_dir} 中搜索")

    if not all_files:
        self.logger.log_operation("批量处理", f"在 {input_dir} 中未找到匹配的文件: {pattern}", level='ERROR')
        return False

    print(f"📊 找到 {len(all_files)} 个文件")

    # 变量过滤
    if target_variables is not None:
        filtered_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            # 从文件名中提取变量名
            var_name = self._extract_variable_from_filename(filename)
            if var_name and var_name in target_variables:
                filtered_files.append(file_path)
        
        files = filtered_files
        print(f"🎯 变量过滤: 从 {len(all_files)} 个文件中筛选出 {len(files)} 个目标变量文件")
        print(f"   目标变量: {target_variables}")
        
        if not files:
            self.logger.log_operation("批量处理", f"未找到包含目标变量的文件: {target_variables}", level='ERROR')
            return False
    else:
        files = all_files
        print(f"📁 处理所有文件: 共 {len(files)} 个文件")

    # 显示文件分布情况
    self._show_file_distribution(files)

    print(f"\n🚀 开始处理 {len(files)} 个文件...")
    success_count = 0
    fail_count = 0
    
    if use_multiprocessing and len(files) > 1:
        # 多核心并行处理
        max_workers = min(self.MAX_WORKERS, len(files))
        print(f"⚡ 使用多进程处理，工作进程数: {max_workers}")
        
        # 准备参数，包含相对路径信息
        args_list = []
        for file_path in files:
            # 计算相对路径以保持目录结构
            rel_path = os.path.relpath(file_path, input_dir)
            rel_dir = os.path.dirname(rel_path)
            target_output_dir = os.path.join(output_dir, rel_dir) if rel_dir else output_dir
            args_list.append((file_path, target_output_dir, self.logger.log_file))
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(process_single_file_worker, args): args[0] for args in args_list}
            
            # 使用tqdm显示进度
            with tqdm(total=len(files), desc="并行处理进度") as pbar:
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        success, message = future.result()
                        if success:
                            success_count += 1
                            # 成功信息只写入日志
                            self.logger.log_operation("处理文件", message, level='INFO')
                        else:
                            fail_count += 1
                            # 失败信息写入日志并可能显示
                            self.logger.log_operation("处理文件", message, level='ERROR')
                    except Exception as e:
                        fail_count += 1
                        self.logger.log_operation("处理文件", f"{os.path.basename(file_path)} -> 异常: {str(e)}", level='ERROR')
                    
                    pbar.update(1)
                    pbar.set_postfix({"成功": success_count, "失败": fail_count})
    else:
        # 单核心顺序处理
        print("🔄 使用单进程顺序处理")
        with tqdm(total=len(files), desc="顺序处理进度") as pbar:
            for file_path in files:
                try:
                    # 计算输出目录，保持相对路径结构
                    rel_path = os.path.relpath(file_path, input_dir)
                    rel_dir = os.path.dirname(rel_path)
                    target_output_dir = os.path.join(output_dir, rel_dir) if rel_dir else output_dir
                    
                    if self.process_new_format_file(file_path, target_output_dir):
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    fail_count += 1
                    continue
                finally:
                    pbar.update(1)
                    pbar.set_postfix({"成功": success_count, "失败": fail_count})

    status = f"处理完成: {success_count} 个成功, {fail_count} 个失败。"
    print(f"\n✅ {status}")
    self.logger.log_operation("批量处理", status, level='INFO')
    return success_count > 0

def _find_files_recursive(self, root_dir, pattern):
    """递归查找匹配的文件"""
    files = []
    for root, dirs, filenames in os.walk(root_dir):
        # 使用fnmatch进行模式匹配
        import fnmatch
        for filename in filenames:
            if fnmatch.fnmatch(filename, pattern):
                files.append(os.path.join(root, filename))
    return files

def _show_file_distribution(self, files):
    """显示文件在各个目录中的分布情况"""
    from collections import Counter
    
    # 统计各目录的文件数量
    dir_counts = Counter()
    var_counts = Counter()
    
    for file_path in files:
        # 目录统计
        dir_name = os.path.dirname(file_path)
        dir_counts[dir_name] += 1
        
        # 变量统计
        filename = os.path.basename(file_path)
        var_name = self._extract_variable_from_filename(filename)
        if var_name:
            var_counts[var_name] += 1
    
    print(f"\n📊 文件分布统计:")
    print("=" * 60)
    
    # 按变量显示
    if var_counts:
        print("🔤 按变量分布:")
        for var_name, count in sorted(var_counts.items()):
            print(f"   {var_name}: {count} 个文件")
    
    # 按目录显示（只显示前10个最多的目录）
    if len(dir_counts) > 1:
        print(f"\n📁 按目录分布 (显示前10个):")
        for dir_name, count in dir_counts.most_common(10):
            # 显示相对路径以节省空间
            rel_dir = os.path.basename(dir_name) if dir_name else "根目录"
            print(f"   {rel_dir}: {count} 个文件")
        
        if len(dir_counts) > 10:
            print(f"   ... 还有 {len(dir_counts) - 10} 个目录")
    
    print("=" * 60)

def _extract_variable_from_filename(self, filename):
    """从文件名中提取变量名
    
    文件名格式: Z_NAFP_C_BABJ_20230101000501_P_HRCLDAS_RT_BENN_0P01_HOR-TAIR-2023010100.GRB2
    提取: TAIR
    """
    try:
        # 匹配模式: *-变量名-*
        pattern = r'HOR-([A-Z0-9]+)-\d{10}'
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
        
        # 备用模式：如果上面的模式不匹配，尝试其他可能的格式
        patterns = [
            r'HOR-([A-Z0-9]+)-',  # 简化模式
            r'-([A-Z0-9]+)-\d{10}',  # 通用模式
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                return match.group(1)
        
        return None
    except Exception as e:
        print(f"⚠️ 文件名解析失败 {filename}: {e}")
        return None

def process_time_series_new_format(self, input_dir, output_dir, start_time, end_time, time_step_hours=1, target_variables=None, recursive=True):
    """处理新格式的时间序列文件 - 使用tqdm显示进度条，支持变量过滤和递归搜索
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        start_time: 开始时间字符串，格式: YYYYMMDDHHMM
        end_time: 结束时间字符串，格式: YYYYMMDDHHMM
        time_step_hours: 时间步长（小时）
        target_variables: 目标变量列表，如 ['TAIR', 'QAIR']，None表示处理所有变量
        recursive: 是否递归搜索子文件夹
    """
    self.logger.start_operation("时间序列处理")

    start_dt = datetime.strptime(start_time, "%Y%m%d%H%M")
    end_dt = datetime.strptime(end_time, "%Y%m%d%H%M")

    time_steps = []
    current_time = start_dt
    while current_time <= end_dt:
        time_steps.append(current_time)
        current_time += timedelta(hours=time_step_hours)

    success_count = 0
    total_files = 0
    
    # 按变量分组存储数据集
    variable_surface_datasets = defaultdict(list)
    variable_upper_datasets = defaultdict(list)

    with tqdm(total=len(time_steps), desc="时间序列处理进度") as pbar:
        for current_time in time_steps:
            time_str = current_time.strftime("%Y%m%d%H")
            
            # 根据是否递归搜索来查找文件
            if recursive:
                pattern = f"*{time_str}*.GRB2"
                time_files = self._find_files_recursive(input_dir, pattern)
            else:
                pattern = f"*{time_str}*.GRB2"
                search_path = os.path.join(input_dir, pattern)
                time_files = glob.glob(search_path)
            
            # 变量过滤
            if target_variables is not None:
                filtered_files = []
                for file_path in time_files:
                    filename = os.path.basename(file_path)
                    var_name = self._extract_variable_from_filename(filename)
                    if var_name and var_name in target_variables:
                        filtered_files.append(file_path)
                time_files = filtered_files

            if time_files:
                for file_path in time_files:
                    total_files += 1
                    filename = os.path.basename(file_path)
                    var_name = self._extract_variable_from_filename(filename)
                    
                    try:
                        surface_ds, upper_ds = self._process_single_grib_file_new_format(file_path)

                        if surface_ds is not None:
                            surface_ds = surface_ds.expand_dims('time')
                            surface_ds.coords['time'] = [current_time]
                            variable_surface_datasets[var_name or 'UNKNOWN'].append(surface_ds)

                        if upper_ds is not None:
                            upper_ds = upper_ds.expand_dims('time')
                            upper_ds.coords['time'] = [current_time]
                            variable_upper_datasets[var_name or 'UNKNOWN'].append(upper_ds)

                        success_count += 1
                    except Exception as e:
                        self.logger.log_operation("时间序列文件处理", f"失败: {os.path.basename(file_path)} - {e}", level='ERROR')
            
            pbar.update(1)
            pbar.set_postfix({"已处理时间点": time_str})

    os.makedirs(output_dir, exist_ok=True)

    # 按变量分别保存文件
    for var_name, datasets in variable_surface_datasets.items():
        if datasets:
            try:
                combined_surface = xr.concat(datasets, dim='time')
                surface_path = os.path.join(output_dir, f"HRCLDAS_TIMESERIES_{var_name}_{start_time}_{end_time}_SURF.nc")
                self._save_dataset_original_format(combined_surface, surface_path, start_dt)
                print(f"✅ 已保存地面数据: {var_name} -> {os.path.basename(surface_path)}")
            except Exception as e:
                self.logger.log_operation("时间序列合并", f"{var_name}地面数据合并失败: {e}", level='ERROR')

    for var_name, datasets in variable_upper_datasets.items():
        if datasets:
            try:
                combined_upper = xr.concat(datasets, dim='time')
                upper_path = os.path.join(output_dir, f"HRCLDAS_TIMESERIES_{var_name}_{start_time}_{end_time}_UPAR.nc")
                self._save_dataset_original_format(combined_upper, upper_path, start_dt)
                print(f"✅ 已保存高空数据: {var_name} -> {os.path.basename(upper_path)}")
            except Exception as e:
                self.logger.log_operation("时间序列合并", f"{var_name}高空数据合并失败: {e}", level='ERROR')

    status = f"时间序列处理完成: {success_count}/{total_files} 个文件成功"
    if target_variables:
        status += f", 目标变量: {target_variables}"
    self.logger.log_operation("时间序列处理", status)
    return success_count > 0

# 将方法添加到类中
ECMWFProcessorAdapted.batch_process_new_format_directory = batch_process_new_format_directory
ECMWFProcessorAdapted._find_files_recursive = _find_files_recursive
ECMWFProcessorAdapted._show_file_distribution = _show_file_distribution
ECMWFProcessorAdapted._extract_variable_from_filename = _extract_variable_from_filename
ECMWFProcessorAdapted.process_time_series_new_format = process_time_series_new_format

# ===== 配置参数 (根据你的实际情况修改) =====

# 示例1: 处理单个新格式文件
# SINGLE_FILE = "../output/HRCLDAS_RT_BENN/QAIR-GRIB/你的单个文件名.GRB2"
# OUTPUT_DIR = "../output/HRCLDAS_RT_BENN/nc/QAIR"
# LOG_FILE = os.path.join(OUTPUT_DIR, "processing_adapted.log")

# 示例2: 批量处理目录
# 从 H_hjy 目录出发，先用 .. 返回上一级，再进入指定的输入输出文件夹
BATCH_INPUT_DIR = "../output/HRCLDAS_RT_BENN/QAIR-GRIB/"
BATCH_OUTPUT_DIR = "../output/HRCLDAS_RT_BENN/nc/QAIR/"
LOG_FILE = os.path.join(BATCH_OUTPUT_DIR, "processing_adapted.log")

# === 变量过滤配置 ===
# 指定要处理的变量，如果为None则处理所有文件
# 常见变量: TAIR(温度), QAIR(湿度), VWIN(风), PRES(气压), PREC(降水)等
TARGET_VARIABLES = ['TAIR', 'QAIR']  # 只处理TAIR和QAIR变量的文件
# TARGET_VARIABLES = ['TAIR']        # 只处理TAIR变量的文件  
# TARGET_VARIABLES = None            # 处理所有变量的文件

# === 递归搜索配置 ===
RECURSIVE_SEARCH = True    # True: 递归搜索所有子文件夹, False: 只搜索指定目录
# 当设置为True时，会在BATCH_INPUT_DIR及其所有子目录中查找GRIB2文件
# 输出时会保持原有的目录结构

# 示例3: 时间序列处理
# TIMESERIES_INPUT_DIR = "../output/HRCLDAS_RT_BENN/QAIR-GRIB/"
# TIMESERIES_OUTPUT_DIR = "../output/HRCLDAS_RT_BENN/nc/QAIR/"
# START_TIME = "202401010000"
# END_TIME = "202401010300"  # 处理4个小时的数据
# TIMESERIES_TARGET_VARIABLES = ['TAIR']  # 时间序列也支持变量过滤
# TIMESERIES_RECURSIVE = True  # 时间序列处理是否递归搜索

# === 多核心处理配置 ===
USE_MULTIPROCESSING = True  # 是否启用多核心并行处理
# MAX_WORKERS 将自动设置为 min(32, CPU核心数-2)，通常不需要手动修改

# ===== 创建处理器 =====
processor = ECMWFProcessorAdapted(LOG_FILE)

# 确保输出目录存在
os.makedirs(BATCH_OUTPUT_DIR, exist_ok=True)

# 模式2: 批量处理目录 (多核心版本，支持变量过滤和递归搜索)
print("🚀 开始执行批量处理...")

if 'BATCH_INPUT_DIR' in locals() and os.path.exists(BATCH_INPUT_DIR):
    print(f"✅ 输入目录存在: {BATCH_INPUT_DIR}")
    print(f"📁 输出目录: {BATCH_OUTPUT_DIR}")
    print(f"🎯 目标变量: {TARGET_VARIABLES}")
    print(f"🔍 递归搜索: {RECURSIVE_SEARCH}")
    print(f"⚡ 多进程处理: {USE_MULTIPROCESSING}")
    print("-" * 60)
    
    try:
        # 开始实际处理
        print("🔄 调用批量处理函数...")
        result = processor.batch_process_new_format_directory(
            BATCH_INPUT_DIR, 
            BATCH_OUTPUT_DIR, 
            use_multiprocessing=USE_MULTIPROCESSING,
            target_variables=TARGET_VARIABLES,  # 变量过滤
            recursive=RECURSIVE_SEARCH  # 递归搜索
        )
        
        if result:
            print("✅ 批量处理完成!")
        else:
            print("❌ 批量处理失败!")
            
    except Exception as e:
        print(f"❌ 批量处理过程中发生错误:")
        print(f"   错误类型: {type(e).__name__}")
        print(f"   错误信息: {str(e)}")
        print(f"   错误详情:")
        import traceback
        traceback.print_exc()
else:
    print(f"❌ 输入目录不存在或未配置:")
    print(f"   BATCH_INPUT_DIR = {locals().get('BATCH_INPUT_DIR', '未配置')}")
    print(f"   目录是否存在: {os.path.exists(locals().get('BATCH_INPUT_DIR', '')) if 'BATCH_INPUT_DIR' in locals() else False}")
    
print("-" * 60)
print("🏁 执行完成")

# ===== 其他处理模式示例 =====

# 模式3: 时间序列处理 (支持变量过滤和递归搜索)
# if 'TIMESERIES_INPUT_DIR' in locals() and os.path.exists(TIMESERIES_INPUT_DIR):
#     try:
#         processor.process_time_series_new_format(
#             TIMESERIES_INPUT_DIR,
#             TIMESERIES_OUTPUT_DIR,
#             START_TIME,
#             END_TIME,
#             target_variables=TIMESERIES_TARGET_VARIABLES,
#             recursive=TIMESERIES_RECURSIVE
#         )
#     except Exception as e:
#         pass

# ===== 目录结构保留说明 =====
print("📁 目录结构保留功能说明:")
print("=" * 60)
print("✅ 当前代码已实现完整的目录结构保留功能!")
print()
print("🔹 工作原理:")
print("   - 使用 os.path.relpath() 计算输入文件相对于输入根目录的相对路径")
print("   - 在输出目录中重建相同的相对路径结构")
print("   - 确保每个输出文件都放在对应的子目录中")
print()
print("🔹 示例:")
print("   输入目录结构:")
print("   📂 input_dir/")
print("      ├── 2024/01/")
print("      │   ├── TAIR文件.GRB2")
print("      │   └── QAIR文件.GRB2")
print("      ├── 2024/02/")
print("      │   ├── TAIR文件.GRB2")
print("      │   └── QAIR文件.GRB2")
print("      └── variables/")
print("          ├── temperature/")
print("          │   └── TAIR_special.GRB2")
print("          └── humidity/")
print("              └── QAIR_special.GRB2")
print()
print("   输出目录结构:")
print("   📂 output_dir/")
print("      ├── 2024/01/")
print("      │   ├── TAIR文件.nc")
print("      │   └── QAIR文件.nc")
print("      ├── 2024/02/")
print("      │   ├── TAIR文件.nc")
print("      │   └── QAIR文件.nc")
print("      └── variables/")
print("          ├── temperature/")
print("          │   └── TAIR_special.nc")
print("          └── humidity/")
print("              └── QAIR_special.nc")
print("=" * 60)

# ===== 变量过滤功能演示 =====
def demo_variable_filtering():
    """演示变量过滤功能"""
    print("\n🎯 变量过滤功能演示:")
    print("=" * 50)
    
    # 测试文件名解析
    test_filenames = [
        "Z_NAFP_C_BABJ_20230101000501_P_HRCLDAS_RT_BENN_0P01_HOR-TAIR-2023010100.GRB2",
        "Z_NAFP_C_BABJ_20230101000459_P_HRCLDAS_RT_BENN_0P01_HOR-VWIN-2023010100.GRB2",
        "Z_NAFP_C_BABJ_20230101000502_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2023010100.GRB2",
        "Z_NAFP_C_BABJ_20230101000503_P_HRCLDAS_RT_BENN_0P01_HOR-PRES-2023010100.GRB2"
    ]
    
    for filename in test_filenames:
        var_name = processor._extract_variable_from_filename(filename)
        print(f"文件: {filename}")
        print(f"  -> 提取到的变量: {var_name}")
        print()
    
    # 演示过滤逻辑
    target_vars = ['TAIR', 'QAIR']
    print(f"如果目标变量是: {target_vars}")
    print("将会处理的文件:")
    
    for filename in test_filenames:
        var_name = processor._extract_variable_from_filename(filename)
        if var_name and var_name in target_vars:
            print(f"  ✅ {filename} (变量: {var_name})")
        else:
            print(f"  ❌ {filename} (变量: {var_name}) - 跳过")

# ===== 递归搜索功能演示 =====
def demo_recursive_search():
    """演示递归搜索功能"""
    print(f"\n🔍 递归搜索功能演示:")
    print("=" * 50)
    
    if 'BATCH_INPUT_DIR' in locals() and os.path.exists(BATCH_INPUT_DIR):
        print(f"搜索目录: {BATCH_INPUT_DIR}")
        
        # 递归查找文件
        all_files = processor._find_files_recursive(BATCH_INPUT_DIR, "*.GRB2")
        
        if all_files:
            print(f"📊 总计找到 {len(all_files)} 个 GRIB2 文件")
            
            # 按目录分组显示
            from collections import defaultdict
            dir_files = defaultdict(list)
            for file_path in all_files[:20]:  # 只显示前20个作为示例
                dir_name = os.path.dirname(file_path)
                filename = os.path.basename(file_path)
                dir_files[dir_name].append(filename)
            
            print(f"\n📁 目录结构示例 (显示前20个文件):")
            for dir_name, filenames in list(dir_files.items())[:5]:  # 只显示前5个目录
                rel_dir = os.path.relpath(dir_name, BATCH_INPUT_DIR)
                print(f"  📂 {rel_dir}/ ({len(filenames)} 个文件)")
                for filename in filenames[:3]:  # 每个目录只显示前3个文件
                    var_name = processor._extract_variable_from_filename(filename)
                    print(f"    📄 {filename} -> {var_name}")
                if len(filenames) > 3:
                    print(f"    ... 还有 {len(filenames) - 3} 个文件")
                print()
                
            # 演示目录结构保留
            print(f"\n📋 目录结构保留演示:")
            if all_files:
                sample_file = all_files[0]
                rel_path = os.path.relpath(sample_file, BATCH_INPUT_DIR)
                rel_dir = os.path.dirname(rel_path)
                filename = os.path.basename(sample_file)
                output_filename = os.path.splitext(filename)[0] + ".nc"
                
                print(f"  输入文件: {rel_path}")
                if rel_dir:
                    print(f"  输出路径: {os.path.join(rel_dir, output_filename)}")
                else:
                    print(f"  输出路径: {output_filename}")
                print("  -> 目录结构完全保持一致! ✅")
        else:
            print("❌ 未找到任何 GRIB2 文件")
    else:
        print(f"❌ 目录不存在或未配置: {locals().get('BATCH_INPUT_DIR', '未配置')}")

# ===== 目录结构验证功能 =====
def verify_directory_structure():
    """验证目录结构保留功能的代码逻辑"""
    print(f"\n🔧 目录结构保留代码验证:")
    print("=" * 50)
    
    # 模拟测试数据
    input_dir = "/input/root"
    output_dir = "/output/root"
    test_files = [
        "/input/root/2024/01/file1.GRB2",
        "/input/root/2024/02/file2.GRB2", 
        "/input/root/variables/temp/file3.GRB2",
        "/input/root/file4.GRB2"
    ]
    
    print("测试文件路径转换:")
    for file_path in test_files:
        # 模拟代码中的路径计算逻辑
        rel_path = os.path.relpath(file_path, input_dir)
        rel_dir = os.path.dirname(rel_path)
        target_output_dir = os.path.join(output_dir, rel_dir) if rel_dir else output_dir
        filename = os.path.basename(file_path)
        output_filename = os.path.splitext(filename)[0] + ".nc"
        final_output_path = os.path.join(target_output_dir, output_filename)
        
        print(f"  输入: {file_path}")
        print(f"  输出: {final_output_path}")
        print(f"  相对路径: {rel_path}")
        print()

# 运行所有演示
demo_variable_filtering()
demo_recursive_search()
verify_directory_structure()