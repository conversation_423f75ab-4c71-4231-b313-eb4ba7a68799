#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
气象变量专题图制作脚本
包含地图三要素和美观样式
"""

import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
import geopandas as gpd
import numpy as np
import os
import glob
import re
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from shapely.geometry import mapping, Point
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'Microsoft YaHei', 'STHeiti']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'dejavusans'
plt.rcParams['mathtext.default'] = 'regular'

class MeteorologicalMapper:
    """气象数据专题图制作类"""
    
    def __init__(self, boundary_path, background_path=None):
        """
        初始化
        
        Parameters:
        boundary_path: str, 边界shapefile路径
        background_path: str, 底图背景shapefile路径
        """
        self.boundary_path = boundary_path
        self.background_path = background_path
        self.boundary = None
        self.background = None
        self.load_boundary()
        self.load_background()
    
    def load_boundary(self):
        """加载边界数据"""
        try:
            self.boundary = gpd.read_file(self.boundary_path)
            print(f"成功加载边界数据: {self.boundary_path}")
            print(f"边界范围: {self.boundary.total_bounds}")
        except Exception as e:
            print(f"加载边界数据失败: {e}")
    
    def load_background(self):
        """加载底图背景数据"""
        if self.background_path is None:
            return
        try:
            self.background = gpd.read_file(self.background_path)
            print(f"成功加载底图背景数据: {self.background_path}")
            print(f"底图背景范围: {self.background.total_bounds}")
        except Exception as e:
            print(f"加载底图背景数据失败: {e}")
    
    def load_nc_data(self, nc_path):
        """
        加载NetCDF数据
        
        Parameters:
        nc_path: str, NetCDF文件路径
        
        Returns:
        xarray.Dataset
        """
        try:
            ds = xr.open_dataset(nc_path)
            ds = ds.load()  # Load data into memory to ensure writability
            print(f"成功加载数据: {nc_path}")
            print(f"数据已加载到内存")
            print(f"数据变量: {list(ds.data_vars)}")
            print(f"数据维度: {ds.dims}")
            return ds
        except Exception as e:
            print(f"加载数据失败: {e}")
            return None
    
    def create_custom_colormap(self, variable_name):
        """
        创建自定义颜色映射 - ESRI风格
        
        Parameters:
        variable_name: str, 变量名
        
        Returns:
        matplotlib colormap
        """
        var_upper = variable_name.upper()
        if any(kw in var_upper for kw in ['QAIR', 'HUMID', '2R', '2SH']):
            # 湿度：ESRI蓝色系 - 从浅到深
            colors = ['#EBF3FD', '#C7E9F1', '#7BCCC4', '#43A2CA', '#2166AC', '#084594']
        elif any(kw in var_upper for kw in ['TAIR', 'TEMP', 'TMP']):
            # 温度：ESRI暖色系 - 从蓝到红
            colors = ['#313695', '#4575B4', '#74ADD1', '#ABD9E9', '#E0F3F8', 
                     '#FFFFBF', '#FEE090', '#FDAE61', '#F46D43', '#D73027', '#A50026']
        else:
            # 默认ESRI绿色系
            colors = ['#F7FCF5', '#E5F5E0', '#C7E9C0', '#A1D99B', '#74C476', 
                     '#41AB5D', '#238B45', '#006D2C', '#00441B']
        
        return LinearSegmentedColormap.from_list('esri_custom', colors, N=256)
    
    def add_map_elements(self, ax, extent):
        """
        添加地图元素（指北针、比例尺等）- ESRI风格
        
        Parameters:
        ax: matplotlib axes
        extent: list, 地图范围 [lon_min, lon_max, lat_min, lat_max]
        """
        # 添加底图背景 - 使用GX.shp作为底图线条
        if self.background is not None:
            self.background.plot(ax=ax, facecolor='none', edgecolor='#CCCCCC', 
                               linewidth=0.8, transform=ccrs.PlateCarree(), alpha=0.7)
        
        # 添加地理要素 - ESRI风格（移除水系，只保留海岸线）
        ax.add_feature(cfeature.COASTLINE, linewidth=0.6, color='#2C5530', alpha=0.6)
        
        # 添加网格线 - ESRI风格
        gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                         linewidth=0.4, color='#CCCCCC', alpha=0.8, linestyle='-')
        gl.top_labels = False
        gl.right_labels = False
        gl.xformatter = LONGITUDE_FORMATTER
        gl.yformatter = LATITUDE_FORMATTER
        gl.xlabel_style = {'size': 9, 'color': '#333333', 'weight': 'normal'}
        gl.ylabel_style = {'size': 9, 'color': '#333333', 'weight': 'normal'}
        
        # 添加指北针 - ESRI风格
        self.add_north_arrow(ax, extent)
        
        # 添加比例尺 - ESRI风格
        self.add_scale_bar(ax, extent)
    
    def add_north_arrow(self, ax, extent):
        """添加指北针 - ESRI风格"""
        # 计算指北针位置（右上角）
        x_pos = extent[1] - (extent[1] - extent[0]) * 0.08
        y_pos = extent[3] - (extent[3] - extent[2]) * 0.08
        arrow_size = (extent[1] - extent[0]) * 0.03
        # 添加指北针箭头 - 改进设计
        arrow = mpatches.FancyArrow(x_pos - arrow_size/2, y_pos - arrow_size/2, 
                                   0, arrow_size, 
                                   width=arrow_size/3, head_width=arrow_size*0.8, 
                                   head_length=arrow_size*0.5, 
                                   transform=ccrs.PlateCarree(),
                                   facecolor='#333333', edgecolor='white', linewidth=1.5)
        ax.add_patch(arrow)
        # 添加'N'文字
        ax.text(x_pos, y_pos + arrow_size/2 + (extent[3] - extent[2]) * 0.01, 'N', 
                transform=ccrs.PlateCarree(),
                fontsize=12, fontweight='bold', ha='center', va='bottom',
                color='#333333')
    
    def add_scale_bar(self, ax, extent):
        """添加比例尺 - ESRI风格"""
        # 计算比例尺位置（左下角）
        x_start = extent[0] + (extent[1] - extent[0]) * 0.03
        y_pos = extent[2] + (extent[3] - extent[2]) * 0.05
        
        # 计算比例尺长度（大约为地图宽度的1/6）
        scale_length = (extent[1] - extent[0]) / 6
        
        # 添加比例尺背景
        bg_width = scale_length * 1.2
        bg_height = (extent[3] - extent[2]) * 0.04
        bg_rect = Rectangle((x_start - scale_length * 0.1, y_pos - bg_height/2), 
                           bg_width, bg_height,
                           transform=ccrs.PlateCarree(),
                           facecolor='white', edgecolor='#333333', 
                           linewidth=1, alpha=0.9)
        ax.add_patch(bg_rect)
        
        # 添加比例尺线条 - 分段式
        segment_length = scale_length / 4
        for i in range(4):
            x_seg_start = x_start + i * segment_length
            color = 'black' if i % 2 == 0 else 'white'
            seg_rect = Rectangle((x_seg_start, y_pos - (extent[3] - extent[2]) * 0.005), 
                               segment_length, (extent[3] - extent[2]) * 0.01,
                               transform=ccrs.PlateCarree(),
                               facecolor=color, edgecolor='black', linewidth=0.5)
            ax.add_patch(seg_rect)
        
        # 添加比例尺标注
        distance_km = scale_length * 111  # 粗略转换为公里
        ax.text(x_start + scale_length/2, y_pos - (extent[3] - extent[2]) * 0.025,
                f'{distance_km:.0f} km', transform=ccrs.PlateCarree(),
                fontsize=9, ha='center', va='top', color='#333333', weight='bold')
    
    def apply_mask(self, data, lons, lats):
        """
        使用边界对数据进行掩膜
        
        Parameters:
        data: numpy array, 数据数组
        lons: numpy array, 经度数组
        lats: numpy array, 纬度数组
        
        Returns:
        numpy array, 掩膜后的数据
        """
        if self.boundary is None:
            return data
        
        try:
            # 复制数据以避免只读错误
            data_copy = np.array(data, copy=True)
            data_copy = data_copy.copy()  # Double copy to ensure a new writable array
            data_copy.flags.writeable = True
            
            # 创建经纬度网格
            lon_2d, lat_2d = np.meshgrid(lons, lats)
            
            # 创建掩膜数组
            mask = np.zeros_like(data_copy, dtype=bool)
            
            print(f"开始掩膜处理，数据形状: {data_copy.shape}")
            
            # 遍历每个点，检查是否在边界内
            for i in range(data_copy.shape[0]):
                for j in range(data_copy.shape[1]):
                    point = Point(lon_2d[i, j], lat_2d[i, j])
                    # 检查点是否在任何一个多边形内
                    is_inside = any(geom.contains(point) for geom in self.boundary.geometry)
                    if not is_inside:
                        mask[i, j] = True
            
            # 应用掩膜
            masked_data = np.ma.masked_array(data_copy, mask=mask, copy=False)
            print(f"掩膜处理完成，掩膜点数: {np.sum(mask)}")
            return masked_data
            
        except Exception as e:
            print(f"掩膜处理失败: {e}")
            return data
    
    def convert_temperature_unit(self, data, variable_name):
        """
        转换温度单位：开尔文转摄氏度
        
        Parameters:
        data: xarray DataArray, 温度数据
        variable_name: str, 变量名
        
        Returns:
        xarray DataArray, 转换后的数据
        """
        if 'TAIR' in variable_name.upper() or 'TMP' in variable_name.upper():
            # 开尔文转摄氏度 - 创建副本以避免只读错误
            data_celsius = data.copy() - 273.15
            print(f"温度已从开尔文转换为摄氏度")
            return data_celsius
        return data
    
    def create_thematic_map(self, nc_path, variable_name, output_path=None, 
                           title=None, unit=None, vmin=None, vmax=None):
        """
        创建专题图
        
        Parameters:
        nc_path: str, NetCDF文件路径
        variable_name: str, 变量名
        output_path: str, 输出图片路径
        title: str, 图片标题
        unit: str, 数据单位
        vmin, vmax: float, 颜色范围
        """
        # 加载数据
        ds = self.load_nc_data(nc_path)
        if ds is None:
            return
        
        # 获取数据变量
        if variable_name not in ds.data_vars:
            print(f"变量 {variable_name} 不在数据中。可用变量: {list(ds.data_vars)}")
            return
        
        data = ds[variable_name]
        
        # 转换温度单位（开尔文转摄氏度）
        data = self.convert_temperature_unit(data, variable_name)
        
        # 如果是3D数据，取第一个时间步或平均值
        if len(data.dims) > 2:
            if 'time' in data.dims:
                data = data.isel(time=0)
            else:
                # 取第一个非空间维度的第一个值
                non_spatial_dims = [dim for dim in data.dims if dim not in ['lat', 'latitude', 'lon', 'longitude', 'x', 'y']]
                if non_spatial_dims:
                    data = data.isel({non_spatial_dims[0]: 0})
        
        # 创建地图
        fig = plt.figure(figsize=(12, 10))
        
        # 设置投影
        proj = ccrs.PlateCarree()
        ax = fig.add_subplot(111, projection=proj)
        
        # 获取数据范围
        if hasattr(data, 'longitude'):
            lon_name, lat_name = 'longitude', 'latitude'
        elif hasattr(data, 'lon'):
            lon_name, lat_name = 'lon', 'lat'
        else:
            # 尝试从坐标中获取
            coord_names = list(data.coords.keys())
            lon_name = next((name for name in coord_names if 'lon' in name.lower()), None)
            lat_name = next((name for name in coord_names if 'lat' in name.lower()), None)
        
        if lon_name is None or lat_name is None:
            print("无法识别经纬度坐标")
            return
        
        lons = data[lon_name].values
        lats = data[lat_name].values
        
        # 应用掩膜
        masked_data = self.apply_mask(data.values, lons, lats)
        
        # 将掩膜数组转换为可写的普通数组，用NaN填充掩膜区域
        if np.ma.is_masked(masked_data):
            writable_data = masked_data.filled(np.nan)
        else:
            writable_data = np.array(masked_data, copy=True)
        
        # 确保数据是可写的
        writable_data = np.array(writable_data, copy=True, dtype=float)
        writable_data.flags.writeable = True
        
        # 设置地图范围
        if self.boundary is not None:
            bounds = self.boundary.total_bounds
            extent = [bounds[0], bounds[2], bounds[1], bounds[3]]
        else:
            extent = [lons.min(), lons.max(), lats.min(), lats.max()]
        
        ax.set_extent(extent, crs=proj)
        
        # 设置颜色范围
        if vmin is None:
            vmin = np.nanpercentile(writable_data, 2)
        if vmax is None:
            vmax = np.nanpercentile(writable_data, 98)
        
        # 创建自定义颜色映射
        cmap = self.create_custom_colormap(variable_name)
        
        # 创建分级颜色映射
        levels = np.linspace(vmin, vmax, 11)  # 10 个级别
        norm = BoundaryNorm(levels, ncolors=cmap.N, clip=True)
        
        # 绘制数据 - ESRI风格
        im = ax.pcolormesh(lons, lats, writable_data, 
                          transform=proj, cmap=cmap, 
                          norm=norm,
                          shading='auto', alpha=0.9)
        
        # 添加地图元素（底图背景等）
        self.add_map_elements(ax, extent)
        
        # 添加县级边界 - 作为最上层，使用简单单线样式
        if self.boundary is not None:
            self.boundary.plot(ax=ax, facecolor='none', edgecolor='#000000', 
                              linewidth=1.0, transform=proj, alpha=1.0)
        
        # 添加颜色条 - ESRI风格，改为垂直在右边
        cbar = plt.colorbar(im, ax=ax, orientation='vertical', 
                           pad=0.02, shrink=0.8, aspect=20,
                           fraction=0.046, anchor=(0.0, 0.5),
                           ticks=levels,
                           boundaries=levels,
                           format='%.2f')
        cbar.ax.tick_params(labelsize=10, colors='#333333', direction='out', length=4)
        
        # 设置颜色条标签
        if unit:
            # 如果是温度且单位是K，改为°C
            if 'TAIR' in variable_name.upper() and unit == 'K':
                unit = r'°C'
            cbar.set_label(r'${}$ (${}$)'.format(variable_name, unit), 
                           fontsize=13, fontweight='bold', color='#333333', usetex=False)
        else:
            cbar.set_label(variable_name, fontsize=13, fontweight='bold', color='#333333')
        
        cbar.outline.set_edgecolor('#333333')
        cbar.outline.set_linewidth(1)
        
        # 设置标题 - ESRI风格
        if title is None:
            title = f'{variable_name} 分布图'
        
        plt.title(title, fontsize=18, fontweight='bold', pad=25, color='#2C5530')
        
        # 设置图形背景色
        fig.patch.set_facecolor('#F8F9FA')
        ax.set_facecolor('#F0F8FF')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if output_path is None:
            output_path = f'{variable_name}_thematic_map.png'
        
        plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"专题图已保存: {output_path}")
        
        plt.show()
        
        # 关闭数据集
        ds.close()


def extract_variable_from_filename(filename):
    """
    从文件名中提取变量名
    
    Parameters:
    filename: str, 文件名
    
    Returns:
    str, 变量名 (如TAIR, QAIR, VWIN等)
    """
    import re
    # 匹配模式: Z_NAFP_C_BABJ_..._HOR-变量名-时间
    pattern = r'HOR-([A-Z]+)-\d+'
    match = re.search(pattern, filename)
    if match:
        return match.group(1)
    return None

def scan_directory_for_variables(directory):
    """
    扫描目录中的文件，识别可用的变量类型
    
    Parameters:
    directory: str, 目录路径
    
    Returns:
    dict, {变量名: [文件列表]}
    """
    import os
    import glob
    
    variables_dict = {}
    
    # 搜索所有.nc文件
    pattern = os.path.join(directory, "*.nc")
    files = glob.glob(pattern)
    
    for file_path in files:
        filename = os.path.basename(file_path)
        variable = extract_variable_from_filename(filename)
        
        if variable:
            if variable not in variables_dict:
                variables_dict[variable] = []
            variables_dict[variable].append(file_path)
    
    return variables_dict

def select_variables_from_directory(directory):
    """
    从目录中扫描并让用户选择要处理的变量
    
    Parameters:
    directory: str, 目录路径
    
    Returns:
    dict, 选中的变量及其文件列表
    """
    print(f"\n🔍 正在扫描目录: {directory}")
    
    variables_dict = scan_directory_for_variables(directory)
    
    if not variables_dict:
        print("❌ 在目录中未发现任何可识别的变量文件")
        return {}
    
    print(f"\n📁 发现以下变量类型:")
    variable_list = list(variables_dict.keys())
    
    for i, var in enumerate(variable_list, 1):
        file_count = len(variables_dict[var])
        print(f"{i}. {var} ({file_count} 个文件)")
    
    print("\n请选择要制作专题图的变量:")
    print("- 输入变量编号，用逗号分隔 (例如: 1,3)")
    print("- 输入 'all' 或 'a' 选择所有变量")
    print("- 输入 'none' 或 'n' 取消操作")
    
    while True:
        try:
            user_input = input("请输入选择: ").strip().lower()
            
            if user_input in ['none', 'n']:
                return {}
            elif user_input in ['all', 'a']:
                return variables_dict
            else:
                # 解析用户输入的编号
                indices = [int(x.strip()) for x in user_input.split(',')]
                selected_variables = {}
                
                for idx in indices:
                    if 1 <= idx <= len(variable_list):
                        var_name = variable_list[idx-1]
                        selected_variables[var_name] = variables_dict[var_name]
                        print(f"✅ 已选择: {var_name} ({len(variables_dict[var_name])} 个文件)")
                    else:
                        print(f"⚠️ 警告: 编号 {idx} 超出范围，已忽略")
                
                if selected_variables:
                    return selected_variables
                else:
                    print("❌ 没有选择有效的变量，请重新输入")
        except ValueError:
            print("❌ 输入格式错误，请重新输入")
        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            return {}

def select_variables_to_plot(available_vars, data_type):
    """
    选择要制作专题图的变量
    
    Parameters:
    available_vars: list, 可用变量列表
    data_type: str, 数据类型(QAIR/TAIR等)
    
    Returns:
    list, 选择的变量列表
    """
    if not available_vars:
        print("没有可用的变量")
        return []
    
    print(f"\n{data_type} 数据中发现以下变量:")
    for i, var in enumerate(available_vars, 1):
        print(f"{i}. {var}")
    
    print("\n请选择要制作专题图的变量:")
    print("- 输入变量编号，用逗号分隔 (例如: 1,3)")
    print("- 输入 'all' 或 'a' 选择所有变量")
    print("- 输入 'none' 或 'n' 跳过此数据类型")
    
    while True:
        try:
            user_input = input("请输入选择: ").strip().lower()
            
            if user_input in ['none', 'n']:
                return []
            elif user_input in ['all', 'a']:
                return available_vars
            else:
                # 解析用户输入的编号
                indices = [int(x.strip()) for x in user_input.split(',')]
                selected_vars = []
                for idx in indices:
                    if 1 <= idx <= len(available_vars):
                        selected_vars.append(available_vars[idx-1])
                    else:
                        print(f"警告: 编号 {idx} 超出范围，已忽略")
                
                if selected_vars:
                    print(f"已选择变量: {selected_vars}")
                    return selected_vars
                else:
                    print("没有选择有效的变量，请重新输入")
        except ValueError:
            print("输入格式错误，请重新输入")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return []

def main_with_file_selection():
    """主函数 - 基于文件名变量选择的版本"""
    
    # 设置路径
    boundary_path = r"F:\HRCLDAS\GIS_DATA\NNX.shp"  # 边界文件
    background_path = r"F:\HRCLDAS\GIS_DATA\GX.shp"  # 底图背景文件
    data_dir = r"F:\HRCLDAS\annual_avg"  # 数据目录
    
    print("🗺️ 气象变量专题图制作工具 (文件名变量选择版)")
    print("="*60)
    
    # 创建制图对象
    mapper = MeteorologicalMapper(boundary_path, background_path)
    
    # 扫描目录并选择变量
    selected_variables = select_variables_from_directory(data_dir)
    
    if not selected_variables:
        print("❌ 未选择任何变量，程序退出")
        return
    
    print(f"\n🎯 开始制作专题图...")
    print(f"📂 数据目录: {data_dir}")
    print(f"🗺️ 边界文件: {boundary_path}")
    print(f"🗺️ 底图文件: {background_path}")
    
    total_success = 0
    total_files = 0
    
    # 为每个选择的变量制作专题图
    for variable_name, file_list in selected_variables.items():
        print(f"\n📊 处理变量: {variable_name}")
        print(f"📁 文件数量: {len(file_list)}")
        
        for file_path in file_list:
            total_files += 1
            filename = os.path.basename(file_path)
            
            try:
                # 加载数据
                ds = xr.open_dataset(file_path)
                available_vars = list(ds.data_vars)
                
                if not available_vars:
                    print(f"⚠️ 文件 {filename} 中没有数据变量")
                    ds.close()
                    continue
                
                # 选择第一个变量（通常nc文件中只有一个主变量）
                main_var = available_vars[0]
                
                # 设置标题和单位
                title = f"2024年年均{variable_name}分布"
                unit = None
                
                # 根据变量类型设置单位
                if variable_name.upper() in ['TAIR', 'TMP']:
                    unit = r'°C'
                elif variable_name.upper() in ['QAIR']:
                    unit = r'kg kg^{-1}'
                elif variable_name.upper() in ['VWIN']:
                    unit = r'm/s'
                elif variable_name.upper() in ['PRES']:
                    unit = r'hPa'
                
                # 生成输出文件名
                output_path = f"thematic_map_{variable_name}_{filename.replace('.nc', '')}.png"
                
                print(f"  🎨 制作专题图: {main_var} -> {output_path}")
                
                # 创建专题图
                mapper.create_thematic_map(
                    nc_path=file_path,
                    variable_name=main_var,
                    output_path=output_path,
                    title=title,
                    unit=unit
                )
                
                total_success += 1
                ds.close()
                
            except Exception as e:
                print(f"❌ 处理文件 {filename} 时出错: {e}")
                continue
    
    print(f"\n✅ 专题图制作完成!")
    print(f"📊 总计: {total_success}/{total_files} 个文件成功处理")

def main():
    """主函数 - 创建所有专题图"""
    
    # 设置路径
    boundary_path = r"F:\HRCLDAS\GIS_DATA\NNX.shp"  # 边界文件
    background_path = r"F:\HRCLDAS\GIS_DATA\GX.shp"  # 底图背景文件
    annual_avg_dir = r"F:\HRCLDAS\annual_avg"
    
    # 变量信息（从data,变量,单位.txt获取）
    variables_info = {
        'QAIR': {
            'files': [
                'Z_NAFP_C_BABJ_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024_annual_avg.nc'
            ],
            'variables': ['2sh', '2r'],  # 比湿和相对湿度
            'units': [r'kg kg^{-1}', r'\%'],
            'titles': ['2024年年均比湿分布', '2024年年均相对湿度分布']
        },
        'TAIR': {
            'files': [
                'Z_NAFP_C_BABJ_P_HRCLDAS_RT_BENN_0P01_HOR-TAIR-2024_annual_avg.nc'
            ],
            'variables': ['TMP_2maboveground'],  # 2米气温
            'units': [r'°C'],  # 已修改为摄氏度
            'titles': ['2024年年均气温分布']
        }
    }
    
    # 创建制图对象
    mapper = MeteorologicalMapper(boundary_path, background_path)
    
    print("开始制作气象专题图...")
    
    # 遍历所有变量制作专题图
    for data_type, info in variables_info.items():
        for file_name in info['files']:
            nc_path = f"{annual_avg_dir}\\{file_name}"
            
            # 加载数据查看可用变量
            try:
                ds = xr.open_dataset(nc_path)
                available_vars = list(ds.data_vars)
                print(f"\n处理文件: {file_name}")
                print(f"发现变量: {available_vars}")
                
                # 让用户选择要制作专题图的变量
                selected_vars = select_variables_to_plot(available_vars, data_type)
                
                if not selected_vars:
                    print(f"跳过 {data_type} 数据类型")
                    ds.close()
                    continue
                
                # 为选择的变量创建专题图
                for var_name in selected_vars:
                    if var_name in available_vars:
                        # 确定单位和标题
                        unit = None
                        title = f"2024年年均{var_name}分布"
                        
                        # 根据变量名匹配单位和标题
                        for j, target_var in enumerate(info['variables']):
                            if target_var in var_name:
                                if j < len(info['units']):
                                    unit = info['units'][j]
                                if j < len(info['titles']):
                                    title = info['titles'][j]
                                break
                        
                        output_path = f"thematic_map_{data_type}_{var_name}_2024.png"
                        
                        print(f"\n制作专题图: {var_name}")
                        mapper.create_thematic_map(
                            nc_path=nc_path,
                            variable_name=var_name,
                            output_path=output_path,
                            title=title,
                            unit=unit
                        )
                    else:
                        print(f"警告: 变量 {var_name} 不在数据中")
                
                ds.close()
                
            except Exception as e:
                print(f"处理文件 {file_name} 时出错: {e}")
    
    print("\n所有专题图制作完成！")


if __name__ == "__main__":
    main()
