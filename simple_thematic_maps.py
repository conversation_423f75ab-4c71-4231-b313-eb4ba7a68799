#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版气象专题图制作脚本
适用于QAIR和TAIR数据
"""

import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LON<PERSON>TUDE_FORMATTER, LATITUDE_FORMATTER
import geopandas as gpd
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as patches
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_colormap(var_type):
    """创建颜色映射"""
    if 'QAIR' in var_type.upper():
        # 湿度：蓝色渐变
        colors = ['#f7fbff', '#c6dbef', '#6baed6', '#2171b5', '#08306b']
    elif 'TAIR' in var_type.upper():
        # 温度：暖色渐变
        colors = ['#ffffb2', '#fed976', '#fd8d3c', '#e31a1c', '#800026']
    else:
        # 默认颜色
        colors = ['#f7fcf0', '#ccebc5', '#7bccc4', '#2b8cbe', '#084081']
    
    return LinearSegmentedColormap.from_list('custom', colors, N=256)

def add_map_elements(ax, extent):
    """添加地图元素"""
    # 添加地理要素
    ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
    ax.add_feature(cfeature.BORDERS, linewidth=0.5)
    
    # 添加网格
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    gl.top_labels = False
    gl.right_labels = False
    gl.xformatter = LONGITUDE_FORMATTER
    gl.yformatter = LATITUDE_FORMATTER
    
    # 指北针
    x_pos = extent[1] - (extent[1] - extent[0]) * 0.08
    y_pos = extent[3] - (extent[3] - extent[2]) * 0.08
    ax.text(x_pos, y_pos, 'N', transform=ccrs.PlateCarree(),
            fontsize=14, fontweight='bold', ha='center', va='center',
            bbox=dict(boxstyle='circle', facecolor='white', edgecolor='black'))
    
    # 比例尺
    x_scale = extent[0] + (extent[1] - extent[0]) * 0.05
    y_scale = extent[2] + (extent[3] - extent[2]) * 0.05
    scale_length = (extent[1] - extent[0]) / 6
    
    ax.plot([x_scale, x_scale + scale_length], [y_scale, y_scale],
            transform=ccrs.PlateCarree(), color='black', linewidth=3)
    
    distance_km = scale_length * 111
    ax.text(x_scale + scale_length/2, y_scale - (extent[3] - extent[2]) * 0.02,
            f'{distance_km:.0f} km', transform=ccrs.PlateCarree(),
            fontsize=10, ha='center', va='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

def plot_meteorological_map(nc_file, var_name, boundary_file, title, unit, output_file):
    """绘制气象专题图"""
    print(f"处理文件: {nc_file}")
    print(f"变量: {var_name}")
    
    # 加载数据
    try:
        ds = xr.open_dataset(nc_file)
        print(f"可用变量: {list(ds.data_vars)}")
        
        # 自动选择变量
        if var_name not in ds.data_vars:
            # 如果指定变量不存在，选择第一个数据变量
            available_vars = [v for v in ds.data_vars if len(ds[v].dims) >= 2]
            if available_vars:
                var_name = available_vars[0]
                print(f"使用变量: {var_name}")
            else:
                print("未找到合适的变量")
                return
        
        data = ds[var_name]
        
        # 处理多维数据
        if len(data.dims) > 2:
            if 'time' in data.dims:
                data = data.isel(time=0)
            else:
                # 选择第一个非空间维度的第一个切片
                spatial_dims = ['lat', 'latitude', 'lon', 'longitude', 'x', 'y']
                for dim in data.dims:
                    if dim not in spatial_dims:
                        data = data.isel({dim: 0})
                        break
        
        # 获取坐标
        coords = list(data.coords.keys())
        lon_coord = next((c for c in coords if 'lon' in c.lower()), None)
        lat_coord = next((c for c in coords if 'lat' in c.lower()), None)
        
        if not lon_coord or not lat_coord:
            print("无法找到经纬度坐标")
            return
        
        lons = data[lon_coord].values
        lats = data[lat_coord].values
        values = data.values
        
        print(f"数据形状: {values.shape}")
        print(f"数据范围: {np.nanmin(values):.3f} - {np.nanmax(values):.3f}")
        
    except Exception as e:
        print(f"加载数据出错: {e}")
        return
    
    # 加载边界
    try:
        boundary = gpd.read_file(boundary_file)
        bounds = boundary.total_bounds
        extent = [bounds[0], bounds[2], bounds[1], bounds[3]]
        print(f"边界范围: {extent}")
    except Exception as e:
        print(f"加载边界出错: {e}")
        extent = [lons.min(), lons.max(), lats.min(), lats.max()]
    
    # 创建图形
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection=ccrs.PlateCarree())
    ax.set_extent(extent, crs=ccrs.PlateCarree())
    
    # 绘制数据
    cmap = create_colormap(var_name)
    vmin, vmax = np.nanpercentile(values, [5, 95])
    
    if len(values.shape) == 2:
        im = ax.pcolormesh(lons, lats, values, 
                          transform=ccrs.PlateCarree(), 
                          cmap=cmap, vmin=vmin, vmax=vmax, 
                          shading='auto', alpha=0.85)
    else:
        print(f"数据维度不正确: {values.shape}")
        return
    
    # 添加边界
    try:
        boundary.plot(ax=ax, facecolor='none', edgecolor='black', 
                     linewidth=2, transform=ccrs.PlateCarree())
    except:
        pass
    
    # 添加地图元素
    add_map_elements(ax, extent)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, orientation='horizontal', 
                       pad=0.1, shrink=0.8, aspect=40)
    cbar.set_label(f'{var_name} ({unit})', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    
    # 设置标题
    plt.title(title, fontsize=18, fontweight='bold', pad=30)
    
    # 添加信息框
    info_text = f'制图日期: 2024年\n数据来源: HRCLDAS\n处理软件: Python'
    ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
            fontsize=9, verticalalignment='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"图片已保存: {output_file}")
    
    plt.show()
    ds.close()

def main():
    """主函数"""
    print("开始制作气象专题图...")
    
    # 文件路径
    boundary_file = r"F:\HRCLDAS\GIS_DATA\NN.shp"
    annual_dir = r"F:\HRCLDAS\annual_avg"
    
    # 要处理的文件和信息
    files_to_process = [
        {
            'file': f"{annual_dir}\\Z_NAFP_C_BABJ_P_HRCLDAS_RT_BENN_0P01_HOR-QAIR-2024_annual_avg.nc",
            'var': 'QAIR',  # 会自动选择实际变量
            'title': '2024年年均比湿分布图',
            'unit': 'kg/kg',
            'output': 'QAIR_2024_annual_map.png'
        },
        {
            'file': f"{annual_dir}\\Z_NAFP_C_BABJ_P_HRCLDAS_RT_BENN_0P01_HOR-TAIR-2024_annual_avg.nc",
            'var': 'TAIR',  # 会自动选择实际变量
            'title': '2024年年均气温分布图',
            'unit': 'K',
            'output': 'TAIR_2024_annual_map.png'
        }
    ]
    
    # 依次处理每个文件
    for item in files_to_process:
        print(f"\n{'='*50}")
        plot_meteorological_map(
            nc_file=item['file'],
            var_name=item['var'],
            boundary_file=boundary_file,
            title=item['title'],
            unit=item['unit'],
            output_file=item['output']
        )
    
    print(f"\n{'='*50}")
    print("所有专题图制作完成！")

if __name__ == "__main__":
    main()
