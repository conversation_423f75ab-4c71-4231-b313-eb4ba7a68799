import xarray as xr
import glob
import os
import re
import pandas as pd
from tqdm import tqdm
import warnings
import gc  # 添加垃圾回收模块

# --- 0. 全局配置 ---
target_variable = "QAIR"
input_dir = "../output/HRCLDAS_RT_BENN_NC/nc/Raw/2023/"

# --- 1. 准备工作 ---
print(f"--- 步骤1: 环境配置与路径设置 (当前目标变量: {target_variable}) ---")
warnings.filterwarnings("ignore", category=UserWarning)
output_dir_monthly = f"../output/HRCLDAS_RT_BENN_NC/nc/Monthly/{target_variable}/2023/"
os.makedirs(output_dir_monthly, exist_ok=True)
print(f"输入目录: {input_dir}")
print(f"最终的月度NC文件将保存到: {output_dir_monthly}")

# --- 2. 基于文件名构建文件索引 ---
print("\n--- 步骤2: 基于文件名构建文件索引 ---")
if not os.path.isdir(input_dir): raise FileNotFoundError(f"错误：指定的输入目录不存在: {input_dir}")
print("正在递归搜索 .NC 和 .nc 文件...")
path_pattern_upper = os.path.join(input_dir, "**/*.NC"); path_pattern_lower = os.path.join(input_dir, "**/*.nc")
file_list = glob.glob(path_pattern_upper, recursive=True) + glob.glob(path_pattern_lower, recursive=True)
if not file_list: raise FileNotFoundError(f"错误：在目录 '{input_dir}' 及其所有子目录中没有找到任何 .NC 或 .nc 文件。")
print(f"在所有子目录中总共找到 {len(file_list)} 个NC文件。")
pattern = re.compile(r'-(\d{10})\.nc$', re.IGNORECASE); file_index = {}; variable_file_count = 0
filter_str = f"-{target_variable}-"
for file_path in file_list:
    if filter_str not in file_path: continue
    if os.path.getsize(file_path) < 100: continue
    variable_file_count += 1
    filename = os.path.basename(file_path); match = pattern.search(filename)
    if match:
        validity_time_str = match.group(1)
        if validity_time_str not in file_index: file_index[validity_time_str] = []
        file_index[validity_time_str].append(file_path)
if not file_index: raise RuntimeError(f"已找到 {variable_file_count} 个'{target_variable}'文件，但未能成功按小时分组。")
print(f"已将 {variable_file_count} 个有效的'{target_variable}'文件按 {len(file_index)} 个独立的小时进行分组。")

# --- 辅助函数：使用回退机制安全地打开单个文件 ---
def open_with_fallback(file_path):
    try: return xr.open_dataset(file_path, engine='netcdf4', cache=False)
    except (OSError, RuntimeError):
        try: return xr.open_dataset(file_path, engine='scipy', cache=False)
        except Exception as e: return f"双引擎打开失败: {e}"

# --- 3 & 4. 按月分块处理和保存 (改进的内存优化) ---
print(f"\n--- 步骤3 & 4: 开始按月分块处理与保存 ---")

# 配置批处理大小 - 按天处理
BATCH_DAYS = 5  # 每次处理5天的数据，可根据内存情况调整

sorted_hours = sorted(file_index.keys())

# 按天分组小时数据
def group_hours_by_day(hours_list):
    """将小时数据按天分组"""
    day_groups = {}
    for hour_str in hours_list:
        date_obj = pd.to_datetime(hour_str, format='%Y%m%d%H')
        day_key = date_obj.strftime('%Y%m%d')  # 获取日期作为键
        if day_key not in day_groups:
            day_groups[day_key] = []
        day_groups[day_key].append(hour_str)
    return day_groups

def process_hour_data(hour_str):
    """处理单个小时的数据，返回处理后的数据集"""
    hour_files = file_index.get(hour_str, [])
    datasets_for_hour = []
    
    for file_path in hour_files:
        ds = open_with_fallback(file_path)
        if isinstance(ds, xr.Dataset):
            datasets_for_hour.append(ds)
    
    if not datasets_for_hour:
        return None
    
    try:
        # 处理当前小时的数据
        ds_for_hour = xr.concat(datasets_for_hour, dim='report', join='override', combine_attrs='override')
        hourly_mean_ds = ds_for_hour.mean(dim='report')
        
        # 立即释放原始数据集的内存
        for ds in datasets_for_hour:
            ds.close()
        
        if 'time' in hourly_mean_ds.dims: 
            hourly_mean_ds = hourly_mean_ds.squeeze(dim='time', drop=True)
        
        time_coord = pd.to_datetime(hour_str, format='%Y%m%d%H')
        final_hourly_ds = hourly_mean_ds.expand_dims(time=[time_coord])
        
        # 释放临时数据集的内存
        hourly_mean_ds.close()
        ds_for_hour.close()
        
        return final_hourly_ds
        
    except Exception as e:
        tqdm.write(f"处理小时 {hour_str} 时发生错误: {e}")
        # 确保在异常情况下也释放内存
        for ds in datasets_for_hour:
            try:
                ds.close()
            except:
                pass
        return None

def process_day_data(day_str, hours_in_day):
    """处理一天的数据，对该天的所有小时求平均"""
    daily_datasets = []
    
    for hour_str in hours_in_day:
        hourly_ds = process_hour_data(hour_str)
        if hourly_ds is not None:
            daily_datasets.append(hourly_ds)
    
    if not daily_datasets:
        return None
    
    try:
        # 合并该天的所有小时数据
        day_combined = xr.concat(daily_datasets, dim='time')
        
        # 对该天的数据求平均，得到日平均值
        daily_mean = day_combined.mean(dim='time')
        
        # 立即释放小时数据集的内存
        for ds in daily_datasets:
            ds.close()
        day_combined.close()
        
        # 为日平均数据添加时间坐标（使用当天00:00作为代表时间）
        day_time = pd.to_datetime(day_str, format='%Y%m%d')
        daily_mean_with_time = daily_mean.expand_dims(time=[day_time])
        
        return daily_mean_with_time
        
    except Exception as e:
        tqdm.write(f"处理日期 {day_str} 时发生错误: {e}")
        # 确保在异常情况下也释放内存
        for ds in daily_datasets:
            try:
                ds.close()
            except:
                pass
        return None

for month_number in tqdm(range(1, 13), desc="正在处理各月份"):
    hours_in_month = [h for h in sorted_hours if pd.to_datetime(h, format='%Y%m%d%H').month == month_number]
    
    if not hours_in_month:
        tqdm.write(f"\n月份 {month_number} 没有找到任何数据，跳过。")
        continue

    # 将该月的小时数据按天分组
    day_groups = group_hours_by_day(hours_in_month)
    day_keys = sorted(day_groups.keys())
    
    # 逐日处理该月的数据，并显示日处理进度条
    monthly_datasets = []
    processed_days = 0
    
    for day_str in tqdm(day_keys, desc=f"  - 处理月份 {month_number:02d}", leave=False):
        hours_in_day = day_groups[day_str]
        daily_ds = process_day_data(day_str, hours_in_day)
        if daily_ds is not None:
            monthly_datasets.append(daily_ds)
            processed_days += 1
    
    # 检查是否有处理成功的数据
    if not monthly_datasets or processed_days == 0:
        tqdm.write(f"\n月份 {month_number} 未能成功处理任何日数据。")
        continue
    
    # 合并所有批次数据为最终的月度数据
    try:
        monthly_data = xr.concat(monthly_datasets, dim='time')
        
        # 立即释放批次数据集的内存
        for ds in monthly_datasets:
            ds.close()
        
        # 保存月度数据
        year = monthly_data.time.dt.year.values[0]
        month_str = f"{month_number:02d}"
        output_filename = f"Z_NAFP_C_BABJ_P_HRCLDAS_RT_BENN_0P01_HOR-{target_variable}-{year}-{month_str}.nc"
        output_path = os.path.join(output_dir_monthly, output_filename)
        monthly_data.to_netcdf(output_path, engine='netcdf4')
        
        # 在主进度条上显示成功信息
        tqdm.write(f"成功保存月份: {year}-{month_str} (处理了 {processed_days} 天数据)")
        
        # 释放月度数据的内存
        monthly_data.close()
        
        # 强制垃圾回收
        gc.collect()
        
    except Exception as e:
        tqdm.write(f"\n保存月份 {month_number} 时发生错误: {e}")
        # 确保在异常情况下也释放内存
        for ds in monthly_datasets:
            try:
                ds.close()
            except:
                pass
        continue
