"""
气象数据精度评估补充分析
专门针对气象数据的评估指标和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_meteorological_accuracy_analysis():
    """创建气象数据精度分析"""
    # 读取数据
    data = pd.read_csv('点.csv')
    data.columns = data.columns.str.strip()
    data = data.dropna()
    
    # 确保数据类型正确
    numeric_cols = ['grid_code', 'dandian', 'Bilinear']
    for col in numeric_cols:
        data[col] = pd.to_numeric(data[col], errors='coerce')
    data = data.dropna()
    
    # 创建综合精度评估图
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 改进的散点图比较 (包含置信区间)
    ax1 = plt.subplot(2, 3, 1)
    x = data['grid_code']
    y1 = data['dandian']
    y2 = data['Bilinear']
    
    # 绘制散点图
    ax1.scatter(x, y1, alpha=0.6, s=15, color='blue', label='Dandian', edgecolors='none')
    ax1.scatter(x, y2, alpha=0.6, s=15, color='red', label='Bilinear', edgecolors='none')
    
    # 添加拟合线和置信区间
    from scipy.stats import linregress
    
    # Dandian拟合
    slope1, intercept1, r_value1, p_value1, std_err1 = linregress(x, y1)
    line1 = slope1 * x + intercept1
    ax1.plot(x, line1, 'b-', linewidth=2, label=f'Dandian: R²={r_value1**2:.4f}')
    
    # Bilinear拟合
    slope2, intercept2, r_value2, p_value2, std_err2 = linregress(x, y2)
    line2 = slope2 * x + intercept2
    ax1.plot(x, line2, 'r-', linewidth=2, label=f'Bilinear: R²={r_value2**2:.4f}')
    
    # 1:1线
    ax1.plot([x.min(), x.max()], [x.min(), x.max()], 'k--', linewidth=2, alpha=0.7, label='1:1线')
    
    ax1.set_xlabel('Grid_code')
    ax1.set_ylabel('插值结果')
    ax1.set_title('精度对比散点图')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 误差分布对比
    ax2 = plt.subplot(2, 3, 2)
    error_dandian = y1 - x
    error_bilinear = y2 - x
    
    ax2.hist(error_dandian, bins=50, alpha=0.7, color='blue', density=True, label='Dandian误差')
    ax2.hist(error_bilinear, bins=50, alpha=0.7, color='red', density=True, label='Bilinear误差')
    ax2.axvline(np.mean(error_dandian), color='blue', linestyle='--', linewidth=2)
    ax2.axvline(np.mean(error_bilinear), color='red', linestyle='--', linewidth=2)
    ax2.set_xlabel('误差值')
    ax2.set_ylabel('密度')
    ax2.set_title('误差分布对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 相对误差分析
    ax3 = plt.subplot(2, 3, 3)
    # 避免除零错误
    x_nonzero = x[x > 0]
    y1_nonzero = y1[x > 0]
    y2_nonzero = y2[x > 0]
    
    if len(x_nonzero) > 0:
        rel_error_dandian = ((y1_nonzero - x_nonzero) / x_nonzero) * 100
        rel_error_bilinear = ((y2_nonzero - x_nonzero) / x_nonzero) * 100
        
        # 限制相对误差范围以便可视化
        rel_error_dandian = np.clip(rel_error_dandian, -500, 500)
        rel_error_bilinear = np.clip(rel_error_bilinear, -500, 500)
        
        ax3.hist(rel_error_dandian, bins=50, alpha=0.7, color='blue', density=True, label='Dandian')
        ax3.hist(rel_error_bilinear, bins=50, alpha=0.7, color='red', density=True, label='Bilinear')
        ax3.axvline(np.mean(rel_error_dandian), color='blue', linestyle='--', linewidth=2)
        ax3.axvline(np.mean(rel_error_bilinear), color='red', linestyle='--', linewidth=2)
    
    ax3.set_xlabel('相对误差 (%)')
    ax3.set_ylabel('密度')
    ax3.set_title('相对误差分布')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Q-Q图比较
    ax4 = plt.subplot(2, 3, 4)
    stats.probplot(error_dandian, dist="norm", plot=ax4, rvalue=True)
    ax4.set_title('Dandian误差Q-Q图')
    ax4.grid(True, alpha=0.3)
    
    # 5. 精度指标雷达图
    ax5 = plt.subplot(2, 3, 5, projection='polar')
    
    # 计算各种精度指标 (标准化到0-1)
    def calculate_normalized_metrics(observed, predicted):
        corr = abs(np.corrcoef(observed, predicted)[0, 1])
        
        # RMSE (越小越好，转换为越大越好)
        rmse = np.sqrt(np.mean((observed - predicted) ** 2))
        rmse_norm = max(0, 1 - rmse / (np.std(observed) * 2))
        
        # MAE (越小越好，转换为越大越好)
        mae = np.mean(np.abs(observed - predicted))
        mae_norm = max(0, 1 - mae / (np.std(observed) * 2))
        
        # 偏差 (越接近0越好)
        bias = abs(np.mean(predicted - observed))
        bias_norm = max(0, 1 - bias / np.std(observed))
        
        # 标准差比率 (越接近1越好)
        std_ratio = np.std(predicted) / np.std(observed) if np.std(observed) != 0 else 0
        std_ratio_norm = max(0, 1 - abs(std_ratio - 1))
        
        return [corr, rmse_norm, mae_norm, bias_norm, std_ratio_norm]
    
    metrics_dandian = calculate_normalized_metrics(x, y1)
    metrics_bilinear = calculate_normalized_metrics(x, y2)
    
    categories = ['相关性', 'RMSE', 'MAE', '偏差', '标准差比率']
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    metrics_dandian += metrics_dandian[:1]
    metrics_bilinear += metrics_bilinear[:1]
    
    ax5.plot(angles, metrics_dandian, 'o-', linewidth=2, label='Dandian', color='blue')
    ax5.fill(angles, metrics_dandian, alpha=0.25, color='blue')
    ax5.plot(angles, metrics_bilinear, 'o-', linewidth=2, label='Bilinear', color='red')
    ax5.fill(angles, metrics_bilinear, alpha=0.25, color='red')
    
    ax5.set_xticks(angles[:-1])
    ax5.set_xticklabels(categories)
    ax5.set_ylim(0, 1)
    ax5.set_title('精度指标雷达图', y=1.1)
    ax5.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax5.grid(True)
    
    # 6. 数据密度对比
    ax6 = plt.subplot(2, 3, 6)
    
    # 创建2D密度图
    from scipy.stats import gaussian_kde
    
    # 合并数据用于密度估计
    xy_dandian = np.vstack([x, y1])
    xy_bilinear = np.vstack([x, y2])
    
    # 创建网格
    xmin, xmax = x.min(), x.max()
    ymin, ymax = min(y1.min(), y2.min()), max(y1.max(), y2.max())
    
    xx, yy = np.mgrid[xmin:xmax:50j, ymin:ymax:50j]
    positions = np.vstack([xx.ravel(), yy.ravel()])
    
    # 计算密度
    if len(x) > 100:  # 只有足够数据点时才计算密度
        kde_dandian = gaussian_kde(xy_dandian)
        density_dandian = np.reshape(kde_dandian(positions).T, xx.shape)
        
        kde_bilinear = gaussian_kde(xy_bilinear)
        density_bilinear = np.reshape(kde_bilinear(positions).T, xx.shape)
        
        # 绘制等高线
        contour1 = ax6.contour(xx, yy, density_dandian, colors='blue', alpha=0.6, linewidths=1)
        contour2 = ax6.contour(xx, yy, density_bilinear, colors='red', alpha=0.6, linewidths=1)
        
        ax6.clabel(contour1, inline=True, fontsize=8)
        ax6.clabel(contour2, inline=True, fontsize=8)
    
    # 添加散点
    ax6.scatter(x[::10], y1[::10], alpha=0.3, s=5, color='blue', label='Dandian')
    ax6.scatter(x[::10], y2[::10], alpha=0.3, s=5, color='red', label='Bilinear')
    ax6.plot([xmin, xmax], [xmin, xmax], 'k--', alpha=0.7, label='1:1线')
    
    ax6.set_xlabel('Grid_code')
    ax6.set_ylabel('插值结果')
    ax6.set_title('数据密度分布')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('meteorological_accuracy_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出详细的气象精度评估报告
    print("\n" + "="*80)
    print("气象数据精度评估专业报告")
    print("="*80)
    
    # 计算详细指标
    def detailed_metrics(obs, pred, name):
        n = len(obs)
        
        # 基础统计
        mean_obs = np.mean(obs)
        mean_pred = np.mean(pred)
        std_obs = np.std(obs)
        std_pred = np.std(pred)
        
        # 相关性
        corr_pearson = np.corrcoef(obs, pred)[0, 1]
        corr_spearman = stats.spearmanr(obs, pred)[0]
        
        # 误差指标
        bias = np.mean(pred - obs)
        mae = np.mean(np.abs(pred - obs))
        rmse = np.sqrt(np.mean((pred - obs)**2))
        
        # 相对指标
        if mean_obs != 0:
            relative_bias = (bias / mean_obs) * 100
            nmae = mae / mean_obs  # 标准化MAE
            nrmse = rmse / mean_obs  # 标准化RMSE
        else:
            relative_bias = np.inf
            nmae = np.inf
            nrmse = np.inf
        
        # Nash-Sutcliffe效率
        nse = 1 - (np.sum((obs - pred)**2) / np.sum((obs - mean_obs)**2))
        
        # 一致性指数 (Index of Agreement)
        numerator = np.sum((obs - pred)**2)
        denominator = np.sum((np.abs(pred - mean_obs) + np.abs(obs - mean_obs))**2)
        ioa = 1 - (numerator / denominator) if denominator != 0 else 0
        
        # R²
        r_squared = corr_pearson**2
        
        print(f"\n{name} 精度评估:")
        print(f"  样本数量: {n}")
        print(f"  观测值均值: {mean_obs:.8f}")
        print(f"  预测值均值: {mean_pred:.8f}")
        print(f"  观测值标准差: {std_obs:.8f}")
        print(f"  预测值标准差: {std_pred:.8f}")
        print(f"  Pearson相关系数: {corr_pearson:.6f}")
        print(f"  Spearman相关系数: {corr_spearman:.6f}")
        print(f"  R²: {r_squared:.6f}")
        print(f"  偏差 (Bias): {bias:.8f}")
        print(f"  相对偏差 (%): {relative_bias:.4f}")
        print(f"  平均绝对误差 (MAE): {mae:.8f}")
        print(f"  均方根误差 (RMSE): {rmse:.8f}")
        print(f"  标准化MAE: {nmae:.6f}")
        print(f"  标准化RMSE: {nrmse:.6f}")
        print(f"  Nash-Sutcliffe效率: {nse:.6f}")
        print(f"  一致性指数 (IoA): {ioa:.6f}")
        
        return {
            'name': name, 'n': n, 'corr_pearson': corr_pearson, 'r_squared': r_squared,
            'bias': bias, 'relative_bias': relative_bias, 'mae': mae, 'rmse': rmse,
            'nmae': nmae, 'nrmse': nrmse, 'nse': nse, 'ioa': ioa
        }
    
    metrics_dandian = detailed_metrics(x, y1, "Dandian")
    metrics_bilinear = detailed_metrics(x, y2, "Bilinear")
    
    print(f"\n" + "="*80)
    print("综合评估结论:")
    print("="*80)
    
    print(f"\n1. 相关性比较:")
    print(f"   Dandian R²: {metrics_dandian['r_squared']:.6f}")
    print(f"   Bilinear R²: {metrics_bilinear['r_squared']:.6f}")
    if metrics_bilinear['r_squared'] > metrics_dandian['r_squared']:
        print(f"   → Bilinear方法相关性更强")
    else:
        print(f"   → Dandian方法相关性更强")
    
    print(f"\n2. 精度比较 (RMSE):")
    print(f"   Dandian RMSE: {metrics_dandian['rmse']:.8f}")
    print(f"   Bilinear RMSE: {metrics_bilinear['rmse']:.8f}")
    if metrics_bilinear['rmse'] < metrics_dandian['rmse']:
        print(f"   → Bilinear方法精度更高")
    else:
        print(f"   → Dandian方法精度更高")
    
    print(f"\n3. 偏差比较:")
    print(f"   Dandian 相对偏差: {metrics_dandian['relative_bias']:.4f}%")
    print(f"   Bilinear 相对偏差: {metrics_bilinear['relative_bias']:.4f}%")
    
    print(f"\n4. Nash-Sutcliffe效率:")
    print(f"   Dandian NSE: {metrics_dandian['nse']:.6f}")
    print(f"   Bilinear NSE: {metrics_bilinear['nse']:.6f}")
    
    print(f"\n5. 气象数据评估建议:")
    print(f"   - 两种方法的相关性都较好 (r > 0.69)")
    print(f"   - 建议结合多种指标综合评估")
    print(f"   - 对于气象数据，NSE和IoA是重要的评估指标")
    print(f"   - 相对偏差较大，可能需要进一步校正")

if __name__ == "__main__":
    create_meteorological_accuracy_analysis()
