#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表数据检查工具
检查SURF_GX_MUL_HOR_TQ表中的数据完整性和时间分布
"""

import pandas as pd
import pyodbc
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_connection_config():
    """加载数据库连接配置"""
    try:
        from sql_connection_config import CONNECTION_STRING
        return CONNECTION_STRING
    except ImportError:
        print("❌ 未找到数据库连接配置文件")
        return None

def check_data_timerange(conn_str):
    """检查数据的时间范围"""
    print("🔍 检查数据库整体时间范围...")
    
    sql = """
    SELECT 
        MIN(DATETIME) AS 最早时间,
        MAX(DATETIME) AS 最晚时间,
        COUNT(*) AS 总记录数,
        COUNT(DISTINCT Station_Id_c) AS 站点数,
        COUNT(DISTINCT CAST(DATETIME AS DATE)) AS 天数
    FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ]
    """
    
    try:
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(sql, conn)
            print("📊 数据库整体统计:")
            for col in df.columns:
                print(f"  {col}: {df[col].iloc[0]}")
            return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def check_2024_monthly_data(conn_str):
    """检查2024年各月份的数据分布"""
    print("\n🔍 检查2024年各月份数据分布...")
    
    sql = """
    SELECT 
        YEAR(DATETIME) AS 年份,
        MONTH(DATETIME) AS 月份,
        COUNT(*) AS 记录数,
        COUNT(DISTINCT Station_Id_c) AS 站点数,
        COUNT(DISTINCT CAST(DATETIME AS DATE)) AS 天数,
        MIN(DATETIME) AS 最早时间,
        MAX(DATETIME) AS 最晚时间
    FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ]
    WHERE YEAR(DATETIME) = 2024
    GROUP BY YEAR(DATETIME), MONTH(DATETIME)
    ORDER BY 年份, 月份
    """
    
    try:
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(sql, conn)
            print("\n📊 2024年各月份数据统计:")
            print(df.to_string(index=False))
            return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def check_target_stations_2024(conn_str):
    """检查目标313个站点在2024年的数据分布"""
    print("\n🔍 检查313个目标站点2024年数据...")
    
    station_ids = [
        'N0435', 'N1003', 'N1006', 'N1009', 'N1010', 'N1013', 'N1014', 'N1015', 'N1016', 'N1017',
        'N1018', 'N1019', 'N1020', 'N1023', 'N1024', 'N1025', 'N1026', 'N1027', 'N1028', 'N1029',
        'N1033', 'N1034', 'N1035', 'N1036', 'N1037', 'N1038', 'N1039', 'N1040', 'N1043', 'N1044',
        'N1045', 'N1046', 'N1047', 'N1048', 'N1049', 'N1050', 'N1053', 'N1054', 'N1055', 'N1056',
        'N1057', 'N1058', 'N1059', 'N1060', 'N1063', 'N1064', 'N1065', 'N1066', 'N1067', 'N1068',
        'N1069', 'N1070', 'N1073', 'N1074', 'N1075', 'N1076', 'N1077', 'N1078', 'N1079', 'N1083',
        'N1085', 'N1086', 'N1087', 'N1088', 'N1089', 'N1090', 'N1093', 'N1095', 'N1096', 'N1097',
        'N1098', 'N1099', 'N1103', 'N1104', 'N1105', 'N1106', 'N1107', 'N1108', 'N1109', 'N1113',
        'N1114', 'N1115', 'N1116', 'N1117', 'N1118', 'N1119', 'N1120', 'N1123', 'N1124', 'N1125',
        'N1128', 'N1129', 'N1130', 'N1133', 'N1134', 'N1135', 'N1137', 'N1138', 'N1139', 'N1140',
        'N1143', 'N1144', 'N1145', 'N1147', 'N1148', 'N1150', 'N1153', 'N1154', 'N1155', 'N1157',
        'N1158', 'N1159', 'N1160', 'N1163', 'N1164', 'N1165', 'N1167', 'N1168', 'N1169', 'N1170',
        'N1173', 'N1174', 'N1175', 'N1178', 'N1179', 'N1180', 'N1183', 'N1184', 'N1185', 'N1187',
        'N1188', 'N1189', 'N1190', 'N1193', 'N1194', 'N1195', 'N1198', 'N1199', 'N1200', 'N1203',
        'N1204', 'N1205', 'N1208', 'N1209', 'N1210', 'N1213', 'N1214', 'N1215', 'N1218', 'N1219',
        'N1223', 'N1224', 'N1225', 'N1227', 'N1228', 'N1229', 'N1230', 'N1233', 'N1234', 'N1235',
        'N1237', 'N1238', 'N1239', 'N1240', 'N1243', 'N1244', 'N1245', 'N1247', 'N1248', 'N1249',
        'N1250', 'N1253', 'N1254', 'N1255', 'N1258', 'N1259', 'N1260', 'N1263', 'N1264', 'N1265',
        'N1268', 'N1269', 'N1273', 'N1274', 'N1275', 'N1277', 'N1278', 'N1279', 'N1280', 'N1283',
        'N1284', 'N1285', 'N1287', 'N1289', 'N1290', 'N1293', 'N1294', 'N1295', 'N1297', 'N1299',
        'N1300', 'N1303', 'N1304', 'N1307', 'N1308', 'N1309', 'N1310', 'N1313', 'N1314', 'N1315',
        'N1317', 'N1319', 'N1320', 'N1323', 'N1324', 'N1325', 'N1327', 'N1329', 'N1330', 'N1333',
        'N1334', 'N1335', 'N1337', 'N1339', 'N1340', 'N1343', 'N1344', 'N1345', 'N1347', 'N1353',
        'N1354', 'N1357', 'N1360', 'N1363', 'N1364', 'N1365', 'N1367', 'N1370', 'N1373', 'N1374',
        'N1377', 'N1380', 'N1384', 'N1387', 'N1390', 'N1393', 'N1394', 'N1397', 'N1400', 'N1403',
        'N1407', 'N1409', 'N1410', 'N1413', 'N1417', 'N1419', 'N1420', 'N1423', 'N1427', 'N1429',
        'N1430', 'N1433', 'N1437', 'N1440', 'N1443', 'N1447', 'N1450', 'N1453', 'N1457', 'N1460',
        'N1463', 'N1467', 'N1470', 'N1473', 'N1477', 'N1480', 'N1487', 'N1490', 'N1497', 'N1500',
        'N1507', 'N1510', 'N1517', 'N1520', 'N1527', 'N1530', 'N1540', 'N1550', 'N1560', 'N1570',
        'N1580', 'N1590', 'N1600', 'N1610', 'N1620', 'N1630', 'N1640', 'N1650', 'N1660', 'N1670',
        'N1680', 'N1690', 'N1700', 'N1710', 'N1730', 'N1740', 'N1750', 'N1760', 'N1770', 'N1780',
        'N1790', 'N1800', 'N1840'
    ]
    
    station_list = "', '".join(station_ids)
    
    sql = f"""
    SELECT 
        MONTH(DATETIME) AS 月份,
        COUNT(*) AS 记录数,
        COUNT(DISTINCT Station_Id_c) AS 有数据站点数,
        COUNT(DISTINCT CAST(DATETIME AS DATE)) AS 天数,
        MIN(DATETIME) AS 最早时间,
        MAX(DATETIME) AS 最晚时间
    FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ]
    WHERE Station_Id_c IN ('{station_list}')
      AND YEAR(DATETIME) = 2024
    GROUP BY MONTH(DATETIME)
    ORDER BY 月份
    """
    
    try:
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(sql, conn)
            print("\n📊 313个目标站点2024年各月份数据:")
            print(df.to_string(index=False))
            return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def check_recent_data(conn_str):
    """检查最近的数据情况"""
    print("\n🔍 检查最近数据情况...")
    
    sql = """
    SELECT TOP 20
        DATETIME,
        Station_Id_c,
        TEM,
        PRS,
        RHU
    FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ]
    ORDER BY DATETIME DESC
    """
    
    try:
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(sql, conn)
            print("\n📊 最近20条记录:")
            print(df.to_string(index=False))
            return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def check_july_august_detail(conn_str):
    """详细检查7-8月的数据"""
    print("\n🔍 详细检查2024年7-8月数据...")
    
    station_ids = ['N0435', 'N1003', 'N1006', 'N1009', 'N1010']  # 取前5个站点作为样本
    station_list = "', '".join(station_ids)
    
    sql = f"""
    SELECT 
        CAST(DATETIME AS DATE) AS 日期,
        COUNT(*) AS 记录数,
        COUNT(DISTINCT Station_Id_c) AS 站点数,
        STRING_AGG(Station_Id_c, ', ') AS 站点列表
    FROM [SURF_DAAS].[dbo].[SURF_GX_MUL_HOR_TQ]
    WHERE Station_Id_c IN ('{station_list}')
      AND YEAR(DATETIME) = 2024
      AND MONTH(DATETIME) IN (7, 8)
    GROUP BY CAST(DATETIME AS DATE)
    ORDER BY 日期
    """
    
    try:
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(sql, conn)
            print(f"\n📊 样本站点(前5个)2024年7-8月逐日数据:")
            print(df.to_string(index=False))
            return df
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None

def create_visualization(monthly_df, output_dir):
    """创建数据分布可视化图表"""
    if monthly_df is None or len(monthly_df) == 0:
        return
    
    print("\n📈 生成数据分布图表...")
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 各月份记录数
    ax1.bar(monthly_df['月份'], monthly_df['记录数'], color='skyblue', alpha=0.7)
    ax1.set_title('2024年各月份记录数分布')
    ax1.set_xlabel('月份')
    ax1.set_ylabel('记录数')
    ax1.grid(True, alpha=0.3)
    
    # 2. 各月份站点数
    ax2.bar(monthly_df['月份'], monthly_df['有数据站点数'], color='lightgreen', alpha=0.7)
    ax2.set_title('2024年各月份有数据站点数')
    ax2.set_xlabel('月份')
    ax2.set_ylabel('站点数')
    ax2.grid(True, alpha=0.3)
    
    # 3. 各月份天数
    ax3.bar(monthly_df['月份'], monthly_df['天数'], color='orange', alpha=0.7)
    ax3.set_title('2024年各月份数据天数')
    ax3.set_xlabel('月份')
    ax3.set_ylabel('天数')
    ax3.grid(True, alpha=0.3)
    
    # 4. 数据完整性评分
    max_days = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]  # 2024年各月最大天数
    completeness = []
    for i, row in monthly_df.iterrows():
        month = int(row['月份']) - 1
        completeness.append(row['天数'] / max_days[month] * 100)
    
    monthly_df['完整性'] = completeness
    colors = ['red' if x < 50 else 'orange' if x < 90 else 'green' for x in completeness]
    ax4.bar(monthly_df['月份'], completeness, color=colors, alpha=0.7)
    ax4.set_title('2024年各月份数据完整性(%)')
    ax4.set_xlabel('月份')
    ax4.set_ylabel('完整性(%)')
    ax4.axhline(y=90, color='red', linestyle='--', alpha=0.5, label='90%基准线')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_path = Path(output_dir) / "数据分布检查_2024.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存: {output_path}")
    
    plt.show()

def main():
    """主函数"""
    print("📊 数据库表数据检查工具")
    print("="*60)
    
    # 加载连接配置
    conn_str = load_connection_config()
    if not conn_str:
        return
    
    # 测试连接
    try:
        test_conn = pyodbc.connect(conn_str, timeout=10)
        test_conn.close()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    # 执行各项检查
    overall_df = check_data_timerange(conn_str)
    monthly_df = check_2024_monthly_data(conn_str)
    target_df = check_target_stations_2024(conn_str)
    recent_df = check_recent_data(conn_str)
    july_aug_df = check_july_august_detail(conn_str)
    
    # 生成可视化图表
    if target_df is not None:
        create_visualization(target_df, "f:/HRCLDAS")
    
    # 保存检查结果
    print("\n💾 保存检查结果...")
    
    results = {
        "检查时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "整体统计": overall_df.to_dict() if overall_df is not None else None,
        "2024年月度统计": monthly_df.to_dict() if monthly_df is not None else None,
        "目标站点统计": target_df.to_dict() if target_df is not None else None
    }
    
    # 保存为CSV
    if target_df is not None:
        target_df.to_csv("f:/HRCLDAS/数据检查结果_2024.csv", index=False, encoding='utf-8-sig')
        print("✅ 检查结果已保存: 数据检查结果_2024.csv")
    
    # 分析结论
    print("\n" + "="*60)
    print("🔍 数据分析结论:")
    print("="*60)
    
    if target_df is not None and len(target_df) > 0:
        # 检查7月后是否有数据
        july_after = target_df[target_df['月份'] > 7]
        if len(july_after) == 0:
            print("⚠️  发现问题: 2024年8月之后确实没有数据!")
        else:
            print("ℹ️  7月后的数据情况:")
            for _, row in july_after.iterrows():
                print(f"    {int(row['月份'])}月: {row['记录数']:,}条记录, {row['有数据站点数']}个站点")
        
        # 数据最全的月份
        max_records_month = target_df.loc[target_df['记录数'].idxmax()]
        print(f"📊 数据最多的月份: {int(max_records_month['月份'])}月 ({max_records_month['记录数']:,}条记录)")
        
        # 数据最少的月份
        min_records_month = target_df.loc[target_df['记录数'].idxmin()]
        print(f"📊 数据最少的月份: {int(min_records_month['月份'])}月 ({min_records_month['记录数']:,}条记录)")
        
        # 完整性分析
        if len(target_df) < 12:
            missing_months = set(range(1, 13)) - set(target_df['月份'])
            print(f"❌ 缺失月份: {', '.join(map(str, sorted(missing_months)))}")
    
    print("="*60)

if __name__ == "__main__":
    main()
